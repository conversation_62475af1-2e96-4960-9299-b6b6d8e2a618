{% layout '' %}
<script src="/assets/js/common.js"></script>
<script>
    {{ Model.Js | raw }}
</script>

<!-- 解析ViewData["VisualPageData"]中的header配置 -->
{% assign visualPageData = ViewData["VisualPageData"] | jsonparse %}
{% assign headerConfig = null %}
{% assign showHeader = true %}
{% assign showSearch = true %}
{% assign showUser = true %}
{% assign showShoppingCart = true %}
{% assign showLanguageSwitch = true %}
{% assign showMenu = true %}
{% assign logoConfig = null %}
{% assign searchPlaceholder = null %}

{% comment %} 从Plugins列表中筛选header类型的插件 {% endcomment %}
{% assign headerPlugin = null %}
{% for plugin in visualPageData.Plugins %}
    {% if plugin.Type == "header" %}
        {% assign headerPlugin = plugin %}
        {% break %}
    {% endif %}
{% endfor %}
{% if headerPlugin %}
    {% assign displayValue = headerPlugin.Config.Display | append: "" %}
    {% if displayValue == "0" %}
        {% assign showHeader = false %}
    {% endif %}

    {% if headerPlugin.Settings %}
        {% assign searchValue = headerPlugin.Settings.Search | append: "" %}
        {% if searchValue == "0" %}
            {% assign showSearch = false %}
        {% endif %}
        {% assign userValue = headerPlugin.Settings.User | append: "" %}
        {% if userValue == "0" %}
            {% assign showUser = false %}
        {% endif %}
        {% assign shoppingCartValue = headerPlugin.Settings.ShoppingCart | append: "" %}
        {% if shoppingCartValue == "0" %}
            {% assign showShoppingCart = false %}
        {% endif %}
        {% assign languageSwitchValue = headerPlugin.Settings.LanguageSwitch | append: "" %}
        {% if languageSwitchValue == "0" %}
            {% assign showLanguageSwitch = false %}
        {% endif %}
        {% if headerPlugin.Settings.SearchPlaceholder %}
            {% assign searchPlaceholder = headerPlugin.Settings.SearchPlaceholder %}
        {% endif %}
    {% endif %}

    {% if headerPlugin.Blocks %}
        {% if headerPlugin.Blocks.Logo %}
            {% assign logoConfig = headerPlugin.Blocks.Logo %}
        {% endif %}
        {% if headerPlugin.Blocks.Menu %}
            {% assign menuValue = headerPlugin.Blocks.Menu.Menu | append: "" %}
            {% if menuValue == "0" %}
                {% assign showMenu = false %}
            {% endif %}
        {% endif %}
    {% endif %}
{% endif %}

<!--调试信息-->
<div style="background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc; font-family: monospace; font-size: 12px;">
    <h4>Header配置调试信息:</h4>
    <p><strong>VisualPageData解析成功:</strong> {% if visualPageData %}是{% else %}否{% endif %}</p>
    <p><strong>Plugins列表存在:</strong> {% if visualPageData.Plugins %}是{% else %}否{% endif %}</p>
    <p><strong>Plugins总数量:</strong> {{ visualPageData.Plugins.size }}</p>
    {% if visualPageData.Plugins.size > 0 %}
        <p><strong>所有插件类型:</strong></p>
        <ul>
        {% for plugin in visualPageData.Plugins %}
            <li>PId: {{ plugin.PId }}, Type: "{{ plugin.Type }}"</li>
        {% endfor %}
        </ul>
    {% endif %}
    {% assign debugHeaderPlugin = null %}
    {% for plugin in visualPageData.Plugins %}
        {% if plugin.Type == "header" %}
            {% assign debugHeaderPlugin = plugin %}
            {% break %}
        {% endif %}
    {% endfor %}
    <p><strong>Header插件找到:</strong> {% if debugHeaderPlugin %}是 (PId: {{ debugHeaderPlugin.PId }}){% else %}否{% endif %}</p>
    {% if debugHeaderPlugin %}
        <p><strong>Header插件配置:</strong></p>
        <ul>
            <li>PId: {{ debugHeaderPlugin.PId }}</li>
            <li>Type: {{ debugHeaderPlugin.Type }}</li>
            <li>Display: {{ debugHeaderPlugin.Config.Display }} → 转换后: "{{ debugHeaderPlugin.Config.Display | append: "" }}"</li>
            <li>Search: {{ debugHeaderPlugin.Settings.Search }} → 转换后: "{{ debugHeaderPlugin.Settings.Search | append: "" }}"</li>
            <li>User: {{ debugHeaderPlugin.Settings.User }} → 转换后: "{{ debugHeaderPlugin.Settings.User | append: "" }}"</li>
            <li>ShoppingCart: {{ debugHeaderPlugin.Settings.ShoppingCart }} → 转换后: "{{ debugHeaderPlugin.Settings.ShoppingCart | append: "" }}"</li>
            <li>Menu: {{ debugHeaderPlugin.Blocks.Menu.Menu }} → 转换后: "{{ debugHeaderPlugin.Blocks.Menu.Menu | append: "" }}"</li>
            <li>Logo: {% if debugHeaderPlugin.Blocks.Logo.Logo %}{{ debugHeaderPlugin.Blocks.Logo.Logo }}{% else %}无{% endif %}</li>
            <li>SearchPlaceholder: {{ debugHeaderPlugin.Settings.SearchPlaceholder }}</li>
        </ul>
    {% else %}
        <p><strong>未找到Header插件</strong></p>
    {% endif %}
    <p><strong>当前配置状态:</strong></p>
    <ul>
        <li>showHeader: {{ showHeader }}</li>
        <li>showSearch: {{ showSearch }}</li>
        <li>showUser: {{ showUser }}</li>
        <li>showShoppingCart: {{ showShoppingCart }}</li>
        <li>showLanguageSwitch: {{ showLanguageSwitch }}</li>
        <li>showMenu: {{ showMenu }}</li>
        <li>logoConfig: {% if logoConfig %}有配置{% else %}无配置{% endif %}</li>
        <li>searchPlaceholder: {{ searchPlaceholder }}</li>
    </ul>
</div>

<!--Marquee Text-->
<div id="head_activities"></div>
<!--<div class="topbar-slider clearfix" style=" background: var(--theme-color); padding: 10px 0;">
    <div class="container-fluid">
        <div class="marquee-text">
            <div class="top-info-bar d-flex">
                <div class="flex-item center"><a href="#;"><span class="flash-icon">⚡</span>T130P – Limited Quantity Remaining! Order Now >></a></div>
                <div class="flex-item center"><a href="#;"><b><span class="flash-icon">🎧</span>Tour Guide System - Get Free Demo Kit >></a></div>
            </div>
        </div>
    </div>
</div>-->
<!--End Marquee Text-->
<!--Header-->
{% if showHeader %}
<div class="topheader">
    <div class="container-fluid">
        <div class="row">
            <div class="col-10 col-sm-8 col-md-5 col-lg-4">
                <p class="email"><a href=""><i style="font-size:20px;" class="icon an an-envelope"></i></a></p>
                <p class="phone-no mx-4"><a href=""><i style="font-size:18px;" class="an an-phone"></i></a></p>
                <p class="phone-no"><a style="color: var(--theme-color);font-weight: bold;"
                                       href="/pages/conviertase-en-distribuidor">Become a Dealer</a></p>
            </div>
            <div class="col-sm-4 col-md-5 col-lg-4 d-none d-md-block d-lg-block">
            </div>
            <div class="col-2 col-sm-4 col-md-2 col-lg-4 text-right">
            </div>
        </div>
    </div>
</div>
<header class="header d-flex align-items-center header-7 header-sticky">
    <div class="container-fluid">
        <div class="row">
            <!--Mobile Icons-->
            <div class="col-4 col-sm-4 col-md-4 d-block d-lg-none mobile-icons">
                <!--Mobile Toggle-->
                <button type="button" class="btn--link site-header__menu js-mobile-nav-toggle mobile-nav--open">
                    <i class="icon an an-times"></i>
                    <i class="an an-bars"></i>
                </button>
                <!--End Mobile Toggle-->
                <!--Search-->
                {% if showSearch %}
                <div class="site-search iconset">
                    <i class="icon an an-search"></i>
                </div>
                {% endif %}
                <!--End Search-->
            </div>
            <!--Mobile Icons-->
            <!--Desktop Logo-->
            <div class="logo col-4 col-sm-4 col-md-4 col-lg-3 align-self-center">
                <a href="/">
                    {% if logoConfig and logoConfig.Logo %}
                        <img src="{{ logoConfig.Logo }}"
                             alt="{% if logoConfig.ImageAlt %}{{ logoConfig.ImageAlt }}{% else %}Logo{% endif %}"
                             title="{% if logoConfig.ImageAlt %}{{ logoConfig.ImageAlt }}{% else %}Logo{% endif %}"
                             class="logo-img"
                             {% if logoConfig.LogoWidth or logoConfig.MobileLogoWidth %}
                             style="{% if logoConfig.LogoWidth %}width: {{ logoConfig.LogoWidth }};{% endif %}{% if logoConfig.MobileLogoWidth %} --mobile-logo-width: {{ logoConfig.MobileLogoWidth }};{% endif %}"
                             {% endif %}/>
                    {% else %}
                        <img src="{{ static_path }}/assets/images/logo/retekess-logo.png" alt="Retekess" title="Retekess" class="logo-img"/>
                    {% endif %}
                </a>
            </div>
            <!--End Desktop Logo-->
            <div class="col-1 col-sm-1 col-md-1 col-lg-6 align-self-center d-menu-col">
                <!--Desktop Menu - 通过HTMX加载-->
                {% if showMenu %}
                <div id="desktop-navigation" hx-get="/home/<USER>" hx-trigger="load" hx-select="nav#AccessibleNav">
                    <!-- 导航菜单将通过HTMX加载 -->
                </div>
                {% endif %}
                <!--End Desktop Menu-->
            </div>
            <div class="col-4 col-sm-4 col-md-4 col-lg-3 align-self-center icons-col text-right">
                <!--Search-->
                {% if showSearch %}
                <div class="site-search iconset">
                    <i class="icon an an-search"></i>
                </div>
                {% endif %}
                {% if showSearch %}
                <div class="search-drawer">
                    <div class="container">
                        <div class="block block-search">
                            <div class="block block-content">
                                <div class="searchField">
                                    <div class="input-box">
                                        <input type="text" name="q" value=""
                                               placeholder="{% if searchPlaceholder %}{{ searchPlaceholder }}{% else %}{{ "blog.global.searchBtn" | translate }}...{% endif %}"
                                               class="input-text">
                                        <button type="submit" title="{{ "blog.global.searchBtn" | translate }}"
                                                class="action search" disabled=""><i class="icon an an-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                <!--End Search-->
                <!--Setting Dropdown-->
                {% if showUser %}
                <div class="setting-link iconset">
                    <i class="icon an an-cog"></i>
                </div>
                {% endif %}
                {% if showUser %}
                <div id="settingsBox">
                    <div class="customer-links">
                        {% if IsLogined == "true" %}
                            <!-- 已登录 -->
                            <div class="dropdown-menu" style="display:block;position:relative;border:none;">
                                <a class="dropdown-item"
                                   href="/Account/MyProfile">{{ "user.account.indexTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyOrders">{{ "user.account.orderTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyInbox">{{ "user.account.inboxTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyAddress">{{ "user.account.addressTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyCoupon">{{ "user.account.couponTitle" | translate }}</a>
                                <a class="dropdown-item"
                                   href="/Account/MyWishList">{{ "user.account.favoriteTitle" | translate }}</a>
                                <!--<a class="dropdown-item"
                                   href="/Account/MyReview">{{ "user.account.reviewTitle" | translate }}</a>-->
                                <a class="dropdown-item"
                                   href="/account/SignOut">{{ "user.account.logOut" | translate }}</a>
                            </div>
                        {% else %}
                            <!-- 未登录 -->
                            <p><a href="/account/signin"
                                  class="btn theme-btn">{{ "user.global.sign_in" | translate }}</a></p>
                            <p class="text-center"><a href="/account/signup"
                                                      class="register">{{ "user.register.register_title" | translate }}</a>
                            </p>
                        {% endif %}

                    </div>

                    {% if showLanguageSwitch %}
                    <div hx-get="/home/<USER>" hx-trigger="load"></div>
                    {% endif %}

                </div>
                {% endif %}
                <!--End Setting Dropdown-->
                <!--Wishlist-->
                <div class="wishlist-link iconset">
                    <a href="/Account/MyWishList">
                        <i class="icon an an-heart-o"></i>
                        <span class="wishlist-count">0</span>
                    </a>
                </div>
                <!--End Wishlist-->
                <!--Minicart Dropdown-->
                {% if showShoppingCart %}
                <div class="header-cart iconset">
                    <a href="/cart" class="site-header__cart btn-minicart">
                        <div class="icon-in">
                            <i class="icon an an-shopping-cart"></i>
                            <span class="site-cart-count">0</span>
                        </div>
                    </a>
                </div>
                {% endif %}
                <!--End Minicart Dropdown-->
            </div>
        </div>
    </div>
</header>
{% endif %}
<!--End Header-->
<!--Mobile Menu - 通过HTMX加载-->
{% if showMenu %}
<div id="mobile-navigation" hx-get="/home/<USER>" hx-trigger="load" hx-select=".mobile-nav-wrapper">
    <!-- 移动端导航菜单将通过HTMX加载 -->
</div>
{% endif %}
<!--End Mobile Menu-->
<!--MiniCart Drawer-->
<!--{% assign miniCart = '/Themes/' | append: theme | append: '/Order/MiniCartDrawer' %}
    {% include miniCart -%}-->
<!--End MiniCart Drawer-->
<input type="hidden" name="operateactivity" id="operateactivity" value="{{ Model.OperateData }}"/>
<script src="/assets/js/operateactivity.js"></script>
<!-- 全局搜索功能脚本 -->
<script>
    // t200主题全局搜索功能实现
    function initGlobalSearchT200() {
        // 获取搜索框和搜索按钮
        const searchInput = document.querySelector('.search-drawer input[name="q"]');
        const searchButton = document.querySelector('.search-drawer .action.search');

        if (!searchInput || !searchButton) {
            console.log('t200搜索元素未找到');
            return;
        }

        // 搜索函数
        function performSearch() {
            const keyword = searchInput.value.trim();
            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }

            // 构建搜索URL - 始终跳转到/shop页面
            let url = '/collections';
            let params = [];

            // 添加keyword参数
            params.push(`keyword=${encodeURIComponent(keyword)}`);

            if (params.length > 0) {
                url += '?' + params.join('&');
            }

            // 关闭搜索抽屉
            document.querySelector('.search-drawer').classList.remove('search-drawer-open');
            document.querySelector('.mask-overlay')?.remove();

            // 跳转到搜索结果页面
            window.location.href = url;
        }

        // 移除按钮的disabled属性
        searchButton.removeAttribute('disabled');

        // 绑定搜索按钮点击事件
        searchButton.addEventListener('click', function (e) {
            e.preventDefault();
            performSearch();
        });

        // 绑定回车键事件
        searchInput.addEventListener('keydown', function (e) {
            if (e.key === 'Enter' || e.keyCode === 13) {
                e.preventDefault();
                performSearch();
            }
        });

    }

    // 页面加载完成后初始化搜索功能
    document.addEventListener('DOMContentLoaded', function () {
        initGlobalSearchT200();
    });

    // 如果是通过HTMX加载的页面，也需要初始化
    document.addEventListener('htmx:afterSwap', function () {
        initGlobalSearchT200();
    });

    $(function () {

        //获取用户配置
        GetUserConf();

    })

    //获取用户配置
    function GetUserConf() {
        $.ajax({
            url: '/Account/GetUserConf',
            method: 'GET',
            contentType: 'application/json',
            success: function (data) {

                $(".wishlist-count").text(data.wishlistCount);
                $(".site-cart-count").text(data.cartCount);
                $(".cart-count").text(data.cartCount);

            },
            error: function (xhr, status, error) {
                // 关闭加载提示
                customize_pop.loadingClose();
                customize_pop.error('Please try again later.', null, null, {showIcon: true});
            },
            complete: function () {
                // 可以在这里添加完成后的处理逻辑
            }
        });
    }

</script>

<!-- 动态Logo样式 -->
{% if logoConfig and logoConfig.MobileLogoWidth %}
<style>
    @media (max-width: 991px) {
        .logo-img {
            width: {{ logoConfig.MobileLogoWidth }} !important;
        }
    }
</style>
{% endif %}