using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.VM.Country;

namespace YseStore.IService.Order
{
    public interface ICountryService : IBaseServices<country>
    {
        /// <summary>
        /// 根据id获取国家信息
        /// </summary>
        /// <param name="countryId"></param>
        /// <returns></returns>
        Task<country> GetCountryAsync(int countryId);

        /// <summary>
        /// 获取国家详情
        /// </summary>
        /// <param name="countryCode"></param>
        /// <returns></returns>
        Task<country> GetCountryAsync(string countryCode);

        /// <summary>
        /// 根据id获取省份信息
        /// </summary>
        /// <param name="provinceId"></param>
        /// <returns></returns>
        Task<country_states_iso> GetProvinceAsync(int provinceId);

        /// <summary>
        /// 获取所有国家信息
        /// </summary>
        /// <returns></returns>
        Task<List<country>> GetCountriesAsync();

        /// <summary>
        /// 获取启用的国家
        /// </summary>
        /// <returns></returns>
        Task<List<country>> GetUsedCountriesAsync();


        /// <summary>
        /// 获取所有国家省市
        /// </summary>
        /// <returns></returns>
        Task<List<country_states_iso>> GetCountryStatesAsync();

        /// <summary>
        /// 更新省
        /// </summary>
        /// <returns></returns>
        Task<bool> UpdateCountryStatesAsync(List<country_states_iso> list);

        /// <summary>
        /// 获取收货地址附加字段
        /// </summary>
        /// <returns></returns>
        Task<List<address_expand>> GetAddressExpandAsync();

        /// <summary>
        /// 清理缓存
        /// </summary>
        /// <returns></returns>
        Task ClearAddressExpandCache();

        /// <summary>
        /// 获取国家下拉框数据
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<string, VM_Country>> GetCountryDropdown();


        /// <summary>
        /// 删除国家缓存和省份缓存
        /// </summary>
        /// <returns></returns>
        Task ClearCountryAndProvinceCache();
    }
}
