using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entitys
{
    public class country_states_iso
    {
        public country_states_iso()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 SId { get; set; }


        public string country_code { get; set; }

        public string States { get; set; }

        public string AcronymCode { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public System.Int32? MyOrder { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public System.Decimal Tax { get; set; }

    }
}
