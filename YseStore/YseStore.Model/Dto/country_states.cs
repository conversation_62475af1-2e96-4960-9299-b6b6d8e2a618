using SqlSugar;

namespace Entitys
{

    /// <summary>
    /// 
    /// </summary>
    public class country_states
    {
        /// <summary>
        /// 
        /// </summary>
        public country_states()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 SId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? CId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String States { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String AcronymCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? MyOrder { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public System.Decimal Tax { get; set; }
    }
}