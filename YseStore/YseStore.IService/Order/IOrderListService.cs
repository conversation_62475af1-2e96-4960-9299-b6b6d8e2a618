using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.VM;
using YseStore.Model;
using Entitys;
using YseStore.Model.Response;
using YseStore.Model.Dto;
using YseStore.Model.VM.Order;
using System.Data;

namespace YseStore.IService.Order
{
    public interface IOrderListService : IBaseServices<orders>
    {
        /// <summary>
        /// 查询用户列表
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="pageNum"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<PagedList<OrderResponse>> QueryAsync(string keywords = "", string OrderStatus = "", string PaymentStatus = "", string ShippingStatus = "", string OrderTime = "", string PaymentTime = "",
                string PId = "", string SId = "", string TId = "", string CreateType = "", string StoreSource = "",string MenuSort="",
            int pageNum = 1,
            int pageSize = 50);

        /// <summary>
        /// 获取订单总价
        /// </summary>
        /// <param name="data"></param>
        /// <param name="isManage"></param>
        /// <returns></returns>
        Task<decimal> CalculateActualPayment(int OrderId, int isManage = 1);
        /// <summary>
        /// 根据userId获取用户信息
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        Task<user> OrdersGetOrderUserInfo(int UserId);
        /// <summary>
        /// 根据userId获取订单信息
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        Task<List<orders>> GetOrderByUserId(int UserId);
        /// <summary>
        /// 根据userId获取用户标签
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        Task<List<user_label_collection>> Getuser_label_collectionByUserId(int UserId);
        /// <summary>
        /// 获取订单标签
        /// </summary>
        /// <returns></returns>
        Task<List<orders_tags>> GetOrders_Tags();
        /// <summary>
        /// 获取订单标签数组
        /// </summary>
        /// <returns></returns>
        Task<List<UserTagDtoResponse>> GetFormattedOrdersTags();
        /// <summary>
        /// 获取物流方式标签
        /// </summary>
        /// <returns></returns>
        Task<List<shipping>> GetShipping();

        /// <summary>
        /// 根据订单号获取用户Id
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        Task<int> GetUserIdByOrderIdAsync(int OrderId);
        /// <summary>
        /// 根据订单号获取订单详情
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        Task<orders> GetOrderDetailByOrderIdAsync(int OrderId);
        /// <summary>
        /// 根据订单号获取删除的订单产品
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        Task<List<orders_products_delete_list>> Getorders_products_delete_list(int OrderId);
        /// <summary>
        /// 根据订单号获取订单日志列表
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        Task<List<orders_log>> GetordersLogListByOrderId(int OrderId);

        /// <summary>
        /// 根据订单id删除产品相关数据
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        Task<bool> OrderdelByOId(int OrderId);
        /// <summary>
        /// 根据订单号获取订单日志列表
        /// </summary>
        /// <param name="OId"></param>
        /// <returns></returns>
        Task<MessageOrdersResponse> GetordersLogListByOId(string OId);
        /// <summary>
        /// 订单消息-管理员回复
        /// </summary>
        /// <param name="Message"></param>
        /// <param name="MsgPicPath"></param>
        /// <param name="MsgVideoPath"></param>
        /// <param name="MId"></param>
        /// <returns></returns>
        Task<UserMessageMessageOrdersResponse> AddordersLogList(string Message, string MsgPicPath, string MsgVideoPath, int MId);
        Task<(object, int)> OrdersTrack(string number, string carrier);
        /// <summary>
        /// 根据订单id返回产品总价相关数据
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns>产品数量-产品价格-积分价格-运费-手续费-订单总价格-货币符号-支付方式</returns>
        Task<(int, decimal, decimal, decimal, decimal, decimal, string, string, decimal, decimal, decimal)> GetOrdersAmount(int orderid);
        /// <summary>
        /// 根据订单id返回退款信息
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        Task<decimal> GetOrderRefundByOrderIdAsync(int OrderId);
        /// <summary>
        /// 根据订单id返回退款列表
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        Task<List<orders_refund_info>> GetOrderRefundByOrderIdList(int OrderId);
        /// <summary>
        /// 获取已经退款的运费
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        Task<decimal> getrefundedShippingAmount(int OrderId);
        /// <summary>
        /// 根据userid获取共个订单，次付款
        /// </summary>
        /// <param name="userid"></param>
        /// <returns></returns>
        Task<(int, int)> GetOrdersPayment(int userid);
        /// <summary>
        /// 根据orderid获取包裹信息
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        Task<List<orders_package>> GetPackageAsync(int orderid);
        /// <summary>
        /// 退款详情中根据orderid获取包裹信息
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        List<orders_package> ProcessOrders(int orderid);
        /// <summary>
        /// 根据wid获取包裹信息
        /// </summary>
        /// <param name="WId"></param>
        /// <returns></returns>
        Task<orders_package> GetPackageByWIdAsync(int WId);
        /// <summary>
        /// 根据orderid获取订单下的产品
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        Task<List<orders_products_list>> GetProducts_Lists(int orderid);
        /// <summary>
        /// 根据orderid获取订单日志
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        Task<List<orders_log>> GetOrders_Logs(int orderid);
        /// <summary>
        /// 根据orderid获取订单备注日志
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        Task<List<orders_remark_log>> GetOrders_Remark_Logs(int orderid);
        /// <summary>
        /// 根据orderid获取订单标签
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        Task<List<orders_tags>> GetOrders_Tags(int orderid);

        /// <summary>
        /// 根据orderid获取订单标签--全部的标签，除了已经标记的
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        Task<List<orders_tags>> GetOrders_TagsBy(int orderid);

        /// <summary>
        /// 获取订单导出菜单
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetOrder_ExportMenuList();
        /// <summary>
        /// 修改订单导出菜单
        /// </summary>
        /// <param name="Value"></param>
        /// <returns></returns>
        Task<bool> UpdetOrder_ExportAry(string Value);
        /// <summary>
        /// 订单状态修改为已完成
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<bool> OrdersStatusSuccess(string id);
        /// <summary>
        /// 获取订单导出菜单String
        /// </summary>
        /// <returns></returns>
        Task<string> GetOrder_ExportMenuStr();

        /// <summary>
        /// 获取订单导出数据
        /// </summary>
        /// <param name="Type"></param>
        /// <param name="Email"></param>
        /// <param name="CurrencyUnit"></param>
        /// <param name="keyword"></param>
        /// <param name="OrderStatus"></param>
        /// <param name="PaymentStatus"></param>
        /// <param name="ShippingStatus"></param>
        /// <param name="OrderTime"></param>
        /// <param name="PaymentTime"></param>
        /// <param name="PId"></param>
        /// <param name="SId"></param>
        /// <param name="TId"></param>
        /// <param name="CreateType"></param>
        /// <param name="StoreSource"></param>
        /// <param name="currentPageCount"></param>
        /// <param name="currentPage"></param>
        /// <param name="id_list"></param>
        /// <param name="refunded"></param>
        /// <param name="shipped"></param>
        /// <param name="outOfStock"></param>
        /// <param name="preSale"></param>
        /// <returns></returns>
        Task<List<OrderExplodeResponse>> GetOrderExplode(string Type, string Email, string CurrencyUnit, string keyword, string OrderStatus, string PaymentStatus, string ShippingStatus,
                    string OrderTime, string PaymentTime, string PId, string SId, string TId, string CreateType, string StoreSource, int currentPageCount, int currentPage, string id_list,
                    string refunded, string shipped, string outOfStock, string preSale,string MenuSort);

        /// <summary>
        /// 保存订单导出csv数据
        /// </summary>
        /// <param name="jsonString"></param>
        /// <param name="orders"></param>
        /// <param name="filePath"></param>
        void ExportToCsv(string jsonString, List<OrderExplodeResponse> orders, string filePath);
        DataTable ExportToCsvTwo(string jsonString, List<OrderExplodeResponse> orders, string filePath);
        // 修改后的原方法（调用新方法）
        void UpLoadFileForLocalStorage(DataTable dt, string filePath);
        /// <summary>
        /// 保存用户csv数据
        /// </summary>
        /// <param name="Name"></param>
        /// <param name="Path"></param>
        /// <returns></returns>
        Task<int> SaveTemporaryStorageFile(string Name, string Path);

        /// <summary>
        /// 根据name获取用户导出文件
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        Task<temporary_storage_file> GetOrderExportDown(string name);

        /// <summary>
        /// 标记发货/修改
        /// </summary>
        /// <param name="WId"></param>
        /// <param name="OrderId"></param>
        /// <param name="TrackingNumber"></param>
        /// <param name="ShippingTime"></param>
        /// <param name="Remarks"></param>
        /// <param name="CarrierRes"></param>
        /// <param name="qtyList"></param>
        /// <returns></returns>
        Task<bool> OrdersModTrack(int WId, int OrderId, string TrackingNumber, string ShippingTime, string Remarks, string CarrierRes, SortedDictionary<string, string> qtyDict);


        /// <summary>
        /// 取消发货
        /// </summary>
        /// <param name="OrderId"></param>
        /// <param name="WId"></param>
        /// <returns></returns>
        Task<bool> OrdersCancelDelivery(int OrderId, int WId);
        /// <summary>
        /// 发送订单备注
        /// </summary>
        /// <param name="OrderId"></param>
        /// <param name="Log"></param>
        /// <returns></returns>
        Task<orders_remark_log> OrdersRemarkLog(int OrderId, string Log);

        /// <summary>
        /// 通过OrderId更新订单表中的标签id
        /// </summary>
        /// <param name="OrderId"></param>
        /// <param name="tagsNameList"></param>
        /// <returns></returns>
        Task<bool> UpdateOrders_tags(int OrderId, List<string> tagsNameList);

        /// <summary>
        /// 修改收货地址
        /// </summary>
        /// <param name="OrderId"></param>
        /// <param name="Type"></param>
        /// <returns></returns>
        Task<OrdersGetAddressResponse> OrdersGetAddress(int OrderId, string Type);

        /// <summary>
        /// 获取国家string
        /// </summary>
        /// <returns></returns>
        Task<List<OrderCountryResponse>> GetCountryData();


        /// <summary>
        /// 获取国家下的省份
        /// </summary>
        /// <returns></returns>
        Task<List<country_states_iso>> GetCountryStatesData();
        /// <summary>
        /// do:获取国家下的省份
        /// </summary>
        /// <param name="SId"></param>
        /// <returns></returns>
        Task<country_states_iso> GetCountryStatesByid(int SId);
        /// <summary>
        /// 通过id获取国家
        /// </summary>
        /// <param name="CId"></param>
        /// <returns></returns>
        Task<country> GetcountryBy(int CId);
        /// <summary>
        /// 获取国家下的省份
        /// </summary>
        /// <returns></returns>
        Dictionary<string, CountryInfoResponse> GetCountryStatesDataTwo();
        /// <summary>
        /// 在订单详情中更改订单状态改为付款
        /// </summary>
        /// <param name="OrderId"></param>
        /// <param name="OrderStatus"></param>
        /// <returns></returns>
        Task<bool> GetOrdersModStatus(int OrderId, int OrderStatus);
        /// <summary>
        /// 修改收货地址 
        /// </summary>
        /// <param name="FirstName"></param>
        /// <param name="LastName"></param>
        /// <param name="AddressLine1"></param>
        /// <param name="AddressLine2"></param>
        /// <param name="City"></param>
        /// <param name="country_id_input"></param>
        /// <param name="country_id"></param>
        /// <param name="country_idType"></param>
        /// <param name="_DoubleOption"></param>
        /// <param name="Province"></param>
        /// <param name="ZipCode"></param>
        /// <param name="CountryCode"></param>
        /// <param name="PhoneNumber"></param>
        /// <param name="tax_code_type"></param>
        /// <param name="tax_code_value"></param>
        /// <param name="OrderId"></param>
        /// <param name="Type"></param>
        /// <returns></returns>
        Task<OrdersModAddressResponse> OrdersModAddress(string FirstName, string LastName, string AddressLine1, string AddressLine2, string City, string country_id_input, int country_id, string country_idType
                , string _DoubleOption, int Province, string ZipCode, string CountryCode, string PhoneNumber, int tax_code_type, string tax_code_value, int OrderId, string Type);

        /// <summary>
        /// 获取比37小的最近一个值（35）
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        Task<int> getprevOrder(int OrderId);

        /// <summary>
        /// 获取比37大的最近一个值（38）
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        Task<int> getnextOrder(int OrderId);
        /// <summary>
        /// 获取店铺Logo
        /// </summary>
        /// <returns></returns>
        Task<string> GetorderPrintLogo();
        /// <summary>
        /// 获取站点名称
        /// </summary>
        /// <returns></returns>
        Task<string> GetSiteName();
        // <summary>
        /// 获取公司名称
        /// </summary>
        /// <returns></returns>
        Task<string> GetCompeny();
        /// <summary>
        /// 获取地址
        /// </summary>
        /// <returns></returns>
        Task<string> GetAddress();
        /// <summary>
        /// 获取邮箱
        /// </summary>
        /// <returns></returns>
        Task<string> GetAdminEmail();
        /// <summary>
        /// 获取电话
        /// </summary>
        /// <returns></returns>
        Task<string> GetTelephone();
        /// <summary>
        /// 获取海外仓库
        /// </summary>
        /// <returns></returns>
        Task<bool> GetConfigOverseas();

        /// <summary>
        /// 获取产品属性
        /// </summary>
        /// <param name="combinations"></param>
        /// <param name="Pro"></param>
        /// <returns></returns>
        string GetProAttribute(List<products_selected_attribute_combination> combinations, products Pro);
        /// <summary>
        /// 获取产品分类
        /// </summary>
        /// <returns></returns>
        Task<List<products_category>> GetProducts_Categories();
        /// <summary>
        /// 获取产品标签
        /// </summary>
        /// <returns></returns>
        Task<List<products_tags>> GetProducts_tags();
        /// <summary>
        /// 获取添加产品--产品信息
        /// </summary>
        /// <param name="Keyword"></param>
        /// <param name="CateId"></param>
        /// <param name="TagId"></param>
        /// <param name="MoreId"></param>
        /// <param name="exclude"></param>
        /// <param name="Page"></param>
        /// <param name="Count"></param>
        /// <returns></returns>
        Task<PagedList<products>> GetProductsChoiceSearchActionV2
            (string Keyword = "", string CateId = "", string TagId = "", string MoreId = "", string exclude = "",
            int Page = 1, int Count = 32);
        /// <summary>
        /// 产品筛选下拉窗构建
        /// </summary>
        /// <returns></returns>
        Task<string> GetSelectProductsFilter(string CateId, string TagId, string MoreId);
        /// <summary>
        /// 根据产品id获取产品信息
        /// </summary>
        /// <param name="ProIdList"></param>
        /// <returns></returns>
        Task<List<products>> GetproductsByProId(List<int> ProIdList);
        /// <summary>
        /// 根据产品id获取产品的库存
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        Task<int> getQtyByProId(int ProId);

        /// <summary>
        /// 根据产品id获取规格属性列表
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        Task<List<products_selected_attribute_combination>> getAttributeByProId(int ProId);
        /// <summary>
        /// 根据产品id获取规格属性列表--用于新增的时候
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        Task<List<products_selected_attribute_combination>> getAttributeTwoByProId(int ProId);
        /// <summary>
        /// 根据产品id获取规格属性收货列表
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        Task<List<products_selected_attribute_combination>> getAttributeShipByProId(int ProId);
        /// <summary>
        /// 根据产品id获取规格属性标题列表
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        Task<List<products_attribute>> getproducts_attributeByProId(int ProId);
        /// <summary>
        /// 根据规格属性id获取规格属性标题
        /// </summary>
        /// <param name="AttrId"></param>
        /// <returns></returns>
        Task<string> getproducts_attributeByAttrId(int AttrId);
        /// <summary>
        /// 根据仓库id获取仓库名称
        /// </summary>
        /// <param name="OvId"></param>
        /// <returns></returns>
        Task<string> getshipping_overseasByOvId(int OvId);
        /// <summary>
        /// 获取仓库列表
        /// </summary>
        /// <returns></returns>
        Task<List<shipping_overseas>> getshipping_overseas();
        /// <summary>
        /// 根据产品id获取仓库列表
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        Task<List<shipping_overseas>> Getshipping_overseasByProId(int ProId);
        /// <summary>
        /// 根据id获取用户地址
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        Task<List<user_address_book>> getuser_address_bookList(int UserId);
        /// <summary>
        /// 获取用户邮箱和id
        /// </summary>
        /// <returns></returns>
        Task<List<CarrierResultResponse>> GetUserBookData();

        /// <summary>
        /// 点击下拉框获取用户邮箱和id
        /// </summary>
        /// <returns></returns>
        Task<PagedList<CarrierResultResponse>> GetNextPageUserBookData(int Start, string Keyword="");

        /// <summary>
        /// 更改/插入用户地址
        /// </summary>
        /// <param name="CustomerValue"></param>
        /// <param name="Customer"></param>
        /// <param name="CustomerType"></param>
        /// <param name="FirstName"></param>
        /// <param name="LastName"></param>
        /// <param name="AddressLine1"></param>
        /// <param name="AddressLine2"></param>
        /// <param name="City"></param>
        /// <param name="country_id_input"></param>
        /// <param name="country_id"></param>
        /// <param name="country_idType"></param>
        /// <param name="_DoubleOption"></param>
        /// <param name="Province"></param>
        /// <param name="ZipCode"></param>
        /// <param name="CountryCode"></param>
        /// <param name="PhoneNumber"></param>
        /// <param name="tax_code_type"></param>
        /// <param name="tax_code_value"></param>
        /// <param name="SaveAddress"></param>
        /// <param name="AddressId"></param>
        /// <returns></returns>
        Task<bool> UpdateUser_address_book(string CustomerValue, string Customer, string CustomerType, string FirstName, string LastName,
            string AddressLine1, string AddressLine2, string City, string country_id_input, string country_id, string country_idType, string _DoubleOption,
            string Province, string ZipCode, string CountryCode, string PhoneNumber, string tax_code_type, string tax_code_value, string SaveAddress, string AddressId);
        /// <summary>
        /// 根据id获取货币
        /// </summary>
        /// <param name="currencyId"></param>
        /// <returns></returns>
        Task<currency> getCurrency(int currencyId);
        /// <summary>
        /// 通过货币缩写，获取货币中的符号
        /// </summary>
        /// <param name="Currency"></param>
        /// <returns></returns>
        Task<string> getCurrencySymbol(string Currency);
        /// <summary>
        /// 获取货币列表
        /// </summary>
        /// <returns></returns>
        Task<List<currency>> getCurrencyList();
        /// <summary>
        /// 根据id获取产品信息
        /// </summary>
        /// <param name="ProId"></param>
        /// <returns></returns>
        Task<products> GetproductById(int ProId);
        /// <summary>
        /// 根据ids获取产品信息
        /// </summary>
        /// <param name="ProIds"></param>
        /// <returns></returns>
        Task<List<products>> GetProductsByIds(List<int?>? ProIds);

        /// <summary>
        /// 根据邮箱获取用户信息
        /// </summary>
        /// <param name="userEmail"></param>
        /// <returns></returns>
        Task<user> GetUserByEmailAsync(string userEmail);
        /// <summary>
        /// 通过id获取支付方式
        /// </summary>
        /// <param name="PId"></param>
        /// <returns></returns>
        Task<payment> GetpaymentByid(int PId);
        /// <summary>
        /// 获取可以使用的支付方式列表
        /// </summary>
        /// <returns></returns>
         Task<List<payment>> GetpaymentList();
        /// <summary>
        /// 新增订单
        /// </summary>
        /// <param name="ordersData"></param>
        /// <returns></returns>
        Task<int> AddOrder(orders ordersData);
        /// <summary>
        /// 获取订单前缀
        /// </summary>
        /// <returns></returns>
        Task<string> GetOrderPrefix();
        /// <summary>
        /// 创建商品订单和包裹信息
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="packageAry"></param>
        /// <param name="isShippingTemplate"></param>
        /// <param name="ShippingMethod"></param>
        /// <param name="ShippingName"></param>
        /// <param name="ShippingPrice"></param>
        /// <returns></returns>
        Task CreateOrdersPackageAsync(int orderId, Dictionary<string, object> packageAry, bool isShippingTemplate,
            List<string> ShippingMethod, List<string> ShippingName, List<string> ShippingPrice, dynamic shippingAry);

        /// <summary>
        /// 创建订单包裹
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="shippingTemplate"></param>
        /// <param name="ShippingMethod"></param>
        /// <param name="ShippingName"></param>
        /// <param name="ShippingPrice"></param>
        /// <param name="shippingAry"></param>
        /// <returns></returns>
        Task CreateOrdersPackageAsyncs(int orderId, bool shippingTemplate,
            List<string> ShippingMethod, List<string> ShippingName, List<string> ShippingPrice, dynamic shippingAry);

        /// <summary>
        /// 记录日志、发送邮件、减库存
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="orderRow"></param>

       void CreateOrderLog(int orderId, orders orderRow);
        /// <summary>
        /// 分配会员
        /// </summary>
        /// <param name="data"></param>
        /// <exception cref="Exception"></exception>
        void AssignMember(orders data, Dictionary<string, object> orderAddress);
        /// <summary>
        /// 根据搜索key获取用户列表
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        Task<List<user>> GetUserByEmail(string keyword);
        /// <summary>
        /// 退款信息中变动信息
        /// </summary>
        /// <param name="orderModelRefundRequest"></param>
        /// <returns></returns>
        Task<OrdersSuggestedRefundResponse> GetOrdersSuggestedRefund(OrderModelRefundRequest orderModelRefundRequest);
        /// <summary>
        /// 操作退款
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<string> AddRefund(OrderModelRefundRequest request);

        /// <summary>
        /// 计算实际支付金额
        /// </summary>
        /// <param name="orderData"></param>
        /// <param name="isManage"></param>
        /// <returns></returns>
        Task<decimal> GetActualPayment(orders orderData, int isManage = 1);
        /// <summary>
        /// 总价格计算
        /// </summary>
        /// <param name="data"></param>
        /// <param name="isFee"></param>
        /// <param name="isManage"></param>
        /// <param name="isRefund"></param>
        /// <param name="isDelete"></param>
        /// <param name="replaceData"></param>
        /// <returns></returns>
        Task<decimal> OrdersPrice(
            orders data, int isFee, int isManage, bool isRefund = true, bool isDelete = true,
            ReplaceDataModel replaceData = null);


        /// <summary>
        /// 订单详情价格计算
        /// </summary>
        /// <param name="data"></param>
        /// <param name="method"></param>
        /// <param name="isManage"></param>
        /// <param name="isRefund"></param>
        /// <param name="isDelete"></param>
        /// <param name="replaceData"></param>
        /// <returns></returns>
        Task<(decimal ProductPrice, decimal DiscountPrice, decimal CouponPrice, decimal PointsPrice, decimal ShippingPrice,
            decimal FeePrice, decimal TaxPrice, decimal TotalPrice)>
            OrdersDetailPrice(orders data, int method, int isManage, bool isRefund, bool isDelete, ReplaceDataModel replaceData = null);
        /// <summary>
        /// 实际付款金额
        /// </summary>
        /// <param name="data"></param>
        /// <param name="IsManage"></param>
        /// <returns></returns>
        Task<decimal> actualPayment(orders data, int IsManage = 1);
        /// <summary>
        /// 获取物流信息
        /// </summary>
        /// <param name="KId"></param>
        /// <param name="orderCreate"></param>
        /// <param name="orderAddress"></param>
        /// <param name="packageAry"></param>
        /// <param name="isShippingTemplate"></param>
        /// <param name="countryId"></param>
        /// <param name="statesSId"></param>
        /// <returns></returns>
        Dictionary<string, object> actionThirdShipping(string KId, Dictionary<string, object> orderCreate,
                Dictionary<string, object> orderAddress, Dictionary<string, object> packageAry, bool isShippingTemplate, int countryId, int statesSId);

        /// <summary>
        /// 写入订单日志数据
        /// </summary>
        /// <param name="type"></param>
        /// <param name="orderData"></param>
        /// <returns></returns>
        //(string Log, string LogTitle, string LogData) WriteOrderLogData(string type, orders orderData);


        /// <summary>
        /// 库存更新逻辑
        /// </summary>
        /// <param name="orderStatus"></param>
        /// <param name="ordersRow"></param>
        /// <param name="type"></param>
        void OrdersProductsUpdate(int orderStatus, orders ordersRow, int type = 0);


        /// <summary>
        /// 获取订单包裹信息
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        (List<orders_package> Parent, Dictionary<int, List<orders_package>> Sub, Dictionary<int, List<orders_package>> Valid,
            List<ProductInfo> ProInfo, int Count, List<int> OvId)
            GetOrdersPackage(int orderId, orders data = null);

        /// <summary>
        /// 取消订单
        /// </summary>
        /// <param name="id"></param>
        /// <param name="totalAmount"></param>
        /// <param name="reason"></param>
        /// <param name="restock"></param>
        /// <param name="actionRefundType"></param>
        /// <param name="paymentMethod"></param>
        /// <returns></returns>
        Task<string> OrdersCancelAction(int id, decimal totalAmount, string reason, int restock, string actionRefundType, string paymentMethod);












    }


  
}
