using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.IService.Visual
{
    public interface IVisualHelpsService
    {
        /// <summary>
        /// 设置宽度容器
        /// </summary>
        /// <param name="ContainerWidth"></param>
        /// <returns></returns>
        string SetContainWidth(string ContainerWidth = "");

        /// <summary>
        /// 计算图片尺寸比例--计算比率
        /// </summary>
        /// <param name="replaceAry"></param>
        /// <param name="type"></param>
        /// <param name="productsPicScale"></param>
        /// <returns></returns>
        string ComputeRatio(Dictionary<string, object> replaceAry = null, string type = "default", string productsPicScale = null);
        /// <summary>
        /// 计算宽度
        /// </summary>
        /// <param name="replaceAry"></param>
        /// <param name="picScale"></param>
        /// <param name="scrollView"></param>
        /// <returns></returns>
        string ComputeWidth(Dictionary<string, object> replaceAry = null, string picScale = "", int scrollView = 0);
        // 获取平铺方式
        string ComputeFilling(Dictionary<string, object> fillingAry = null);


    }
}
