using Entitys;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.Common.Const;
using YseStore.IService.Order;
using YseStore.Model.VM.Country;
using YseStore.Model.VM.Payment.Paypal;

namespace YseStore.Service.Order
{
    public class CountryService : BaseServices<country>, ICountryService
    {
        private readonly ILogger<country> _logger;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly IStringLocalizer<CountryService> _localizer;
        public CountryService(ILogger<country> logger, IConfiguration configuration, ISqlSugarClient db, ICaching caching, IStringLocalizer<CountryService> localizer)
        {
            _logger = logger;
            _configuration = configuration;
            this.db = db;
            _caching = caching;
            _localizer = localizer;
        }
        /// <summary>
        /// 根据id获取国家信息
        /// </summary>
        /// <param name="countryId"></param>
        /// <returns></returns>
        public async Task<country> GetCountryAsync(int countryId)
        {
            //获取hash缓存
            var currencyItem = _caching.HashGet<country>(GlobalConstVars.CountryHash, countryId.ToString());
            if (currencyItem != null)
            {
                return currencyItem;
            }


            var countryList = await GetCountriesAsync();
            var ret = countryList
                .Where(x => x.CId == countryId)
                .FirstOrDefault();
            if (ret != null)
            {
                //将国家信息存入缓存
                _caching.HashSet(GlobalConstVars.CountryHash, countryId.ToString(), ret);
            }

            return ret;
        }

        /// <summary>
        /// 根据国家简码获取详情
        /// </summary>
        /// <param name="countryCode"></param>
        /// <returns></returns>
        public async Task<country> GetCountryAsync(string countryCode)
        {
            //获取hash缓存
            var currencyItem = _caching.HashGet<country>(GlobalConstVars.CountryHash, countryCode);
            if (currencyItem != null)
            {
                return currencyItem;
            }


            var countryList = await GetCountriesAsync();
            var ret = countryList
                .Where(x => x.Acronym == countryCode)
                .FirstOrDefault();
            if (ret != null)
            {
                //将国家信息存入缓存
                _caching.HashSet(GlobalConstVars.CountryHash, countryCode, ret);
            }

            return ret;
        }

        /// <summary>
        /// 根据id获取省份信息
        /// </summary>
        /// <param name="provinceId"></param>
        /// <returns></returns>
        public async Task<country_states_iso> GetProvinceAsync(int provinceId)
        {
            //获取hash缓存
            var currencyItem = _caching.HashGet<country_states_iso>(GlobalConstVars.CountryStatesHash, provinceId.ToString());
            if (currencyItem != null)
            {
                return currencyItem;
            }

            var stateList = await GetCountryStatesAsync();

            var ret = stateList
                .Where(x => x.SId == provinceId)
                .FirstOrDefault();
            if (ret != null)
            {
                //将省份信息存入缓存
                _caching.HashSet(GlobalConstVars.CountryStatesHash, provinceId.ToString(), ret);
            }
            return ret;
        }


        /// <summary>
        /// 获取所有国家缓存信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<country>> GetCountriesAsync()
        {

            //从缓存中获取国家列表
            var cacheKey = GlobalConstVars.CountryList;
            var countryList = await _caching.GetAsync<List<country>>(cacheKey);
            if (countryList == null)
            {
                // 如果缓存中没有数据，则从数据库查询
                countryList = await db.Queryable<country>()
                    .OrderBy(c => c.Country, OrderByType.Asc) // 按 Country 升序排列
                    .ToListAsync();
                // 将查询结果存入缓存
                await _caching.SetAsync(cacheKey, countryList); // 
            }
            return countryList;
        }

        /// <summary>
        /// 获取启用的
        /// </summary>
        /// <returns></returns>
        public async Task<List<country>> GetUsedCountriesAsync()
        {
            var ret = await _caching.GetFromCacheAsync(GlobalConstVars.UsedCountryList, async () =>
            {
                var list = await GetCountriesAsync();
                list = list.Where(u => u.IsUsed == true).ToList();
                return list;
            });

            return ret;
        }

        /// <summary>
        /// 获取省州
        /// </summary>
        /// <returns></returns>
        public async Task<List<country_states_iso>> GetCountryStatesAsync()
        {


            //从缓存中获取省州列表
            var cacheKey = GlobalConstVars.CountryStatesList;
            var stateList = await _caching.GetAsync<List<country_states_iso>>(cacheKey);
            if (stateList == null)
            {
                // 如果缓存中没有数据，则从数据库查询
                stateList = await db.Queryable<country_states_iso>()
                        .OrderBy(c => c.MyOrder, OrderByType.Asc)
                    .OrderBy(c => c.States, OrderByType.Asc) // 按 Country 升序排列
                    .ToListAsync();
                // 将查询结果存入缓存
                await _caching.SetAsync(cacheKey, stateList); // 
            }
            return stateList;

        }


        /// <summary>
        /// 更新省
        /// </summary>
        /// <returns></returns>
        public async Task<bool> UpdateCountryStatesAsync(List<country_states_iso> list)
        {
            if (list.Any())
            {
                var update = await db.Updateable<country_states_iso>(list).ExecuteCommandAsync();

                await _caching.DelByPatternAsync(GlobalConstVars.CountryStatesList);

                return update > 0;
            }

            return false;

        }


        /// <summary>
        /// 获取收货地址附加字段
        /// </summary>
        /// <returns></returns>
        public async Task<List<address_expand>> GetAddressExpandAsync()
        {
            var cacheKey = GlobalConstVars.AddressExpList;
            var addressList = await _caching.GetAsync<List<address_expand>>(cacheKey);
            if (addressList == null)
            {
                // 如果缓存中没有数据，则从数据库查询
                addressList = await db.Queryable<address_expand>()
                    .ToListAsync();
                // 将查询结果存入缓存
                await _caching.SetAsync(cacheKey, addressList); // 
            }
            return addressList;
        }


        public async Task ClearAddressExpandCache()
        {
            await _caching.DelByPatternAsync(GlobalConstVars.AddressExpList); //删除地址附加字段缓存

        }

        /// <summary>
        /// 获取国家下拉框数据
        /// </summary>
        /// <returns></returns>
        public async Task<Dictionary<string, VM_Country>> GetCountryDropdown()
        {

            var cacheKey = GlobalConstVars.CountryDropdown;
            var countryDropdownData = await _caching.GetAsync<Dictionary<string, VM_Country>>(cacheKey);
            if (countryDropdownData == null)
            {
                countryDropdownData = new Dictionary<string, VM_Country>();
                //获取启用的国家
                var countryList = await GetUsedCountriesAsync();

                //获取所有省州
                var countryStateList = await GetCountryStatesAsync();

                //获取地址附加字段
                var address = await GetAddressExpandAsync();

                //遍历国家
                foreach (var country in countryList)
                {
                    VM_Country vM_Country = new VM_Country
                    {
                        name = country.Country,
                        code = country.Acronym,
                        tax = country.Tax.ToString(),
                        threshold = country.TaxThreshold,
                        phone_code = country.Code.Value,
                        address_format = country.AddressFormat,
                        labels = new Dictionary<string, string>(),
                        provinces = new Dictionary<string, Province>(),
                        field = null,
                    };
                    if (!country.AddressLabels.IsNullOrEmpty())
                    {
                        var labelList = country.AddressLabels.JsonToObj<List<string>>();
                        foreach (var label in labelList)
                        {
                            string addlabel = _localizer[$"address.{country.Acronym}.{label}"];

                            vM_Country.labels.Add(label, addlabel);
                        }
                    }

                    var countryFields = address.Where(it => (it.IsOpen || it.Scope == "global") && it.Country == country.Acronym).FirstOrDefault();
                    if (countryFields != null)
                    {
                        vM_Country.field = new Dictionary<string, object>
                        {
                            {countryFields.Param,countryFields.Data}
                        };
                    }
                    var itemStateList = countryStateList.Where(it => it.country_code == country.Acronym).ToList();
                    //遍历省州
                    foreach (var state in itemStateList)
                    {
                        vM_Country.provinces.Add($"_{state.SId}", new Province
                        {
                            code = state.AcronymCode,
                            name = state.States,
                            tax = state.Tax.ToString(),
                            SId = state.SId,
                        });
                    }

                    countryDropdownData.Add(country.CId.ToString(), vM_Country);
                }


                // 将查询结果存入缓存
                await _caching.SetAsync(cacheKey, countryDropdownData); // 
            }

            return countryDropdownData;

        }


        /// <summary>
        /// 删除国家和省州缓存
        /// </summary>
        /// <returns></returns>
        public async Task ClearCountryAndProvinceCache()
        {
            await _caching.DelByPatternAsync(GlobalConstVars.CountryList);//删除国家列表缓存
            await _caching.DelByPatternAsync(GlobalConstVars.CountryStatesList);//删除省州列表缓存
            await _caching.DelByPatternAsync(GlobalConstVars.CountryHash);//删除国家hash缓存
            await _caching.DelByPatternAsync(GlobalConstVars.CountryStatesHash);//删除省州hash缓存
            await _caching.DelByPatternAsync(GlobalConstVars.CountryDropdown);//删除国家下拉框缓存
            await _caching.DelByPatternAsync(GlobalConstVars.UsedCountryList);//删除启用的国家

            
        }
    }
}
