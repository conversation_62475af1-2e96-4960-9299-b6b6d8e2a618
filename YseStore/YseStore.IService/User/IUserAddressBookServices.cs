using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model;

namespace YseStore.IService.User
{
    /// <summary>
    /// 用户收货地址接口
    /// </summary>
    public interface IUserAddressBookServices : IBaseServices<user_address_book>
    {

        /// <summary>
        /// 获取用户收货地址
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<List<user_address_book>> GetUserAddressBook(int userId);
        /// <summary>
        /// 获取国家string
        /// </summary>
        /// <returns></returns>
        Task<List<OrderCountryResponse>> GetCountryData();
        /// <summary>
        /// 获取第一个国家下的州
        /// </summary>
        /// <returns></returns>
        Task<List<country_states_iso>> GetCountry_statesData();
        /// <summary>
        /// 根据CId获取一个国家下的州
        /// </summary>
        /// <returns></returns>
        Task<List<country_states_iso>> GetCountry_statesDataByCId(int CId);
        /// <summary>
        /// 通过CId获取国家信息
        /// </summary>
        /// <param name="CId"></param>
        /// <returns></returns>
        Task<country> GetCountryDataByCId(int CId);
        /// <summary>
        /// 获取国家信息列表
        /// </summary>
        /// <param name="CId"></param>
        /// <returns></returns>
        Task<List<country>> GetCountryDataList();
        /// <summary>
        /// 根据SId获取第一个省份
        /// </summary>
        /// <param name="SId"></param>
        /// <returns></returns>
        Task<country_states_iso> GetCountry_statesDataBySId(int SId);

        /// <summary>
        /// 设置收货地址的默认地址
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="aId"></param>
        /// <returns></returns>
        Task<bool> SetDefaultAddress(int userId, int aId);








    }
}
