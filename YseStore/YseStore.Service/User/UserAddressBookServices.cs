using Entitys;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService.User;
using YseStore.Model;
using YseStore.Repo;

namespace YseStore.Service.User
{
    /// <summary>
    /// 用户收货地址服务
    /// </summary>
    public class UserAddressBookServices : BaseServices<user_address_book>, IUserAddressBookServices
    {

        private readonly ILogger<UserAddressBookServices> _logger;
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;

        public UserAddressBookServices(ILogger<UserAddressBookServices> logger, ISqlSugarClient db, ICaching caching)
        {
            _logger = logger;
            this.db = db;
            _caching = caching;
        }


        /// <summary>
        /// 获取用户收货地址
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<user_address_book>> GetUserAddressBook(int userId)
        {
            var list = await db.Queryable<user_address_book>()
                .LeftJoin<country>((a, c) => a.CId == c.CId)
                .LeftJoin<country_states_iso>((a, c, s) => a.SId == s.SId)
                .Where((a, c, s) => a.UserId == userId && a.IsBillingAddress == false)
                .Select((a, c, s) => new user_address_book
                {
                    Country = c.Country,
                    Acronym = c.Acronym,
                    StateName = s.States,
                    CTax = c.Tax,
                    STax = c.Tax,
                    TaxThreshold = c.TaxThreshold,

                }, isAutoFill: true).ToListAsync();

            return list;
        }

        /// <summary>
        /// 获取国家string
        /// </summary>
        /// <returns></returns>
        public async Task<List<OrderCountryResponse>> GetCountryData()
        {
            var countrys = await db.Queryable<country>()
                .Where(x => x.IsUsed == true)
                .OrderBy(x => x.Country)

                .ToListAsync();
            var result = new List<OrderCountryResponse>();
            foreach (var item in countrys)
            {
                result.Add(new OrderCountryResponse
                {
                    Name = item.Country,
                    Value = item.CId,
                    Type = "country"
                });
            }
            return result;

        }
        /// <summary>
        /// 获取第一个国家下的州
        /// </summary>
        /// <returns></returns>
        public async Task<List<country_states_iso>> GetCountry_statesData()
        {
            var countrys = await db.Queryable<country>()
                .Where(x => x.IsUsed == true)
                .Select(x => new { x.Acronym, x.Country })
                .OrderBy(x => x.Country)
                .ToListAsync();
            var CId = countrys.FirstOrDefault();
            if (CId == null)
            {
                return new List<country_states_iso>();
            }
            var result = await db.Queryable<country_states_iso>()
                .Where(x =>x.country_code == CId.Acronym).ToListAsync();
            return result;
        }
        /// <summary>
        /// 根据CId获取一个国家下的州
        /// </summary>
        /// <returns></returns>
        public async Task<List<country_states_iso>> GetCountry_statesDataByCId(int CId)
        {
            var country = await db.Queryable<country>()
                .Where(x => x.IsUsed == true && x.CId==CId)
                .Select(x=>x.Acronym)
                .FirstAsync();
            if(country==null)
            {
                return new List<country_states_iso>();
            }

            var result = await db.Queryable<country_states_iso>()
                .Where(x => x.country_code == country).ToListAsync();
            return result;
        }
        /// <summary>
        /// 通过CId获取国家信息
        /// </summary>
        /// <param name="CId"></param>
        /// <returns></returns>
        public async Task<country> GetCountryDataByCId(int CId)
        {
            var result = await db.Queryable<country>()
                .Where(x => x.CId == CId).FirstAsync();
            return result;
        }
        /// <summary>
        /// 获取国家信息列表
        /// </summary>
        /// <param name="CId"></param>
        /// <returns></returns>
        public async Task<List<country>> GetCountryDataList()
        {
            var result = await db.Queryable<country>()
                .ToListAsync();
            return result;
        }
        /// <summary>
        /// 根据SId获取第一个省份
        /// </summary>
        /// <param name="SId"></param>
        /// <returns></returns>
        public async Task<country_states_iso> GetCountry_statesDataBySId(int SId)
        {
            var result = await db.Queryable<country_states_iso>()
                .Where(x => x.SId == SId).FirstAsync();
            return result;
        }

        /// <summary>
        /// 设置收货地址的默认地址
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="aId"></param>
        /// <returns></returns>
        public async Task<bool> SetDefaultAddress(int userId,int aId)
        {

           var b=  db.Updateable<user_address_book>()
                .SetColumns(x => x.IsDefault == false)
                .Where(x => x.UserId == userId && x.IsBillingAddress == false)
                .ExecuteCommandHasChange();
            if (b)
            {
                db.Updateable<user_address_book>()
                .SetColumns(x => x.IsDefault == true)
                .Where(x => x.AId == aId)
                .ExecuteCommandHasChange();
            }

            return b;
        }





    }
}
