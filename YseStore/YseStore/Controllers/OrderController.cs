using Aop.Api.Domain;
using Elasticsearch.Net;
using Entitys;
using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Localization;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using Org.BouncyCastle.Asn1.Ocsp;
using Org.BouncyCastle.Ocsp;
using StackExchange.Redis;
using System.Security.Cryptography;
using System.Threading.Tasks;
using System.Web;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.Common.Const;
using YseStore.Common.Helper;
using YseStore.Ext;
using YseStore.IService;
using YseStore.IService.Blog;
using YseStore.IService.Order;
using YseStore.IService.Pay;
using YseStore.IService.Products;
using YseStore.IService.Sales;
using YseStore.IService.Set;
using YseStore.IService.Shopping;
using YseStore.IService.User;
using YseStore.Model;
using YseStore.Model.FromBody;
using YseStore.Model.RequestModels.Set;
using YseStore.Model.Response.Set;
using YseStore.Model.VM;
using YseStore.Model.VM.Cart;
using YseStore.Model.VM.Country;
using YseStore.Model.VM.Order;
using YseStore.Model.VM.Payment.Payoneer;
using YseStore.Model.VM.Payment.Paypal;
using YseStore.Model.VM.Sales;
using YseStore.Service;
using YseStore.Service.Order;
using ZXing;
using ProductInfo = YseStore.Model.VM.Order.ProductInfo;

namespace YseStore.Controllers
{
    public class OrderController : BaseController
    {
        private readonly IShoppingCartServices _cartServices;
        private readonly IProductService _productService;
        private readonly IOrderService _orderService;
        private readonly ICurrencyService _currencyService;
        private readonly ICountryService _countryService;
        private readonly IConfigService _configService;
        private readonly IMemberPointsService _memberPointsService;
        private readonly IUserService _userService;
        private readonly ICommonService _commonService;
        private readonly IStringLocalizer<OrderController> _localizer;
        private readonly IUserAddressBookServices _userAddressBookServices;
        private readonly YseStore.IService.Pay.IPaymentService _paymentService;
        private readonly IShippingMethodService _shippingMethodThirdService;
        private readonly ISalesCouponService _salesCouponService;
        private readonly IShippingWarehouseService _shippingWarehouseService;
        private readonly IOrderListService _orderListService;
        private readonly IOrderProductsService _orderProductsService;
        private readonly IHelpsCartService _helpsCartService;
        private readonly ICaching _caching;

        public OrderController(IShoppingCartServices shoppingCartServices, IProductService productService, IOrderService orderService, ICurrencyService currencyService, ICountryService countryService, IConfigService configService, IMemberPointsService memberPointsService, IUserService userService, ICommonService commonService, IStringLocalizer<OrderController> localizer, IUserAddressBookServices userAddressBookServices, YseStore.IService.Pay.IPaymentService paymentService, IShippingMethodService shippingMethodThirdService, ISalesCouponService salesCouponService, IShippingWarehouseService shippingWarehouseService, IOrderListService orderListService, IOrderProductsService orderProductsService, IHelpsCartService helpsCartService, ICaching caching)
        {
            _cartServices = shoppingCartServices;
            _productService = productService;
            _orderService = orderService;
            _currencyService = currencyService;
            _countryService = countryService;
            _configService = configService;
            _memberPointsService = memberPointsService;
            _userService = userService;
            _commonService = commonService;
            _localizer = localizer;
            _userAddressBookServices = userAddressBookServices;
            _paymentService = paymentService;
            _shippingMethodThirdService = shippingMethodThirdService;
            _salesCouponService = salesCouponService;
            _shippingWarehouseService = shippingWarehouseService;
            _orderListService = orderListService;
            _orderProductsService = orderProductsService;
            _helpsCartService = helpsCartService;
            _caching = caching;
        }

        /// <summary>
        /// 订单结算页面
        /// </summary>
        /// <returns></returns>
        [Route("/cart/checkout")]
        //[Authorize]
        public async Task<IActionResult> Address()
        {
            string data = Request.GetQueryString("data");

            string cIdStr = Request.GetQueryString("CId");
            /*
             * 不合并购物车 访客登录后用到，
             * 不合并时根据访客购物车的产品、数量进行下单，
             * 将访客购物车的userid改为当前登录用户的，将sessionid清空
             * 访问购物车时合并同一产品数量
             */
            int noMergeCart = Request.GetQueryInt("noMergeCart");
            int buynow = Request.GetQueryInt("buynow"); //立即购买

            string idparam = HttpUtility.UrlDecode(data.FromBase64());
            if (idparam.IsNullOrEmpty())
            {
                return Redirect("/cart");
            }
            List<int> idList = new List<int>();
            List<string> ids = idparam.Split(',').ToList();
            if (ids.Count == 0)
            {
                return Redirect("/cart");
            }
            foreach (var id in ids)
            {
                var s = int.TryParse(id, out int CId);
                if (s == false || CId == 0)
                {
                    return Redirect("/cart");
                }
                idList.Add(CId);
            }


            //获取所有国家
            List<country> countryList = await _countryService.GetCountriesAsync();

            #region 购物车产品
            //获取选中的商品
            var r = new WebApiCallBack<CartDto>();
            if (!cIdStr.IsNullOrEmpty())
            {
                //链接参数中的购物车id
                List<string> CIdStringList = cIdStr.Split('.').ToList();
                CIdStringList.Remove("0");
                if (CIdStringList.Count > 0)
                {

                    List<int> CIdList = new List<int>();

                    foreach (var idStr in CIdStringList)
                    {
                        var s = int.TryParse(idStr, out int CId);
                        if (s == false || CId == 0)
                        {
                            return Redirect("/cart");
                        }
                        CIdList.Add(CId);
                    }

                    if (CurrentUserId > 0)
                    {
                        //更新购物车UserID
                        var upCart = await _cartServices.UpdateCartUser(CurrentUserId, CIdList);
                        if (upCart.status)
                        {
                            //重新赋值选中的id
                            idList = CIdList;
                        }

                        //if (noMergeCart == 0)//合并购物车
                        //{
                        //    await _cartServices.MergeCar(CurrentUserId);
                        //}

                    }

                }

            }
            //获取用户币种
            var currency = await _currencyService.GetCurrency(CurrentCurrency);

            //获取后台币种
            var defaultCurrency = await _currencyService.GetManageDefaultCurrency();


            if (CurrentUserId > 0)//在线客户
            {
                r = await _cartServices.GetCartInfos(CurrentUserId, currency, defaultCurrency, idList);
            }
            else
            {
                r = await _cartServices.GetCartInfos(CurrentUserSessionId, currency, defaultCurrency, idList);
            }

            if (r.status == false)
            {
                return Redirect("/cart");
            }


            ////获取币种
            //var currency = await _currencyService.GetCurrency(CurrentCurrency);

            ////获取后台币种
            //var defaultCurrency = await _currencyService.GetManageDefaultCurrency();

            //foreach (var item in r.data.list)
            //{
            //    item.oldPriceFormat = _currencyService.ShowPriceFormat(item.oldPrice, currency, defaultCurrency);
            //    item.priceFormat = _currencyService.ShowPriceFormat(item.price, currency, defaultCurrency);
            //    item.itemTotalFormat = _currencyService.ShowPriceFormat(item.price * item.nums, currency, defaultCurrency);
            //}

            //r.data.goodsAmountFormat = _currencyService.ShowPriceFormat(r.data.goodsAmount, currency, defaultCurrency); //商品总价
            //r.data.amountFormat = _currencyService.ShowPriceFormat(r.data.amount, currency, defaultCurrency);//订单总计
            //r.data.orderPromotionMoneyFormat = _currencyService.ShowPriceFormat(r.data.orderPromotionMoney, currency, defaultCurrency);//优惠总计

            //r.data.list = r.data.list.OrderByDescending(it => it.createTime).OrderBy(it => it.isInvalid).ToList();
            //r.data.failureList = r.data.list.Where(it => it.isInvalid).ToList();
            //r.data.ProductList = r.data.list.Where(it => it.isInvalid == false).ToList();


            int oversea_count = 0;


            //获取仓库id 
            var OvIdList = r.data.ProductList.Select(it => it.cart.OvId).Distinct().ToList();

            //获取物流id
            var TIdList = r.data.ProductList.Select(it => it.products.TId).Distinct().ToList();

            var cart_aryList = new List<cart_ary>();

            //获取所有仓库
            var overList = await _shippingWarehouseService.GetAllWarehousesAsync();

            //仓库
            foreach (var OvId in OvIdList)
            {
                cart_ary cartAry = new cart_ary();
                cartAry.OvId = OvId;
                //cartAry.BuyType=

                //仓库
                var overseas = overList.Where(it => it.OvId == OvId).FirstOrDefault();
                if (overseas != null)
                {
                    cartAry.Overseas = overseas.Name;
                }

                cartAry.cart_Ary_Tids = new List<cart_ary_tid>();
                //获取仓库下的产品
                var ovProductList = r.data.ProductList.Where(it => it.cart.OvId == OvId && idList.Contains(it.CId)).ToList();

                //遍历物流
                foreach (var TId in TIdList)
                {
                    cart_ary_tid cartTId = new cart_ary_tid();
                    cartTId.TId = TId;

                    //获取物流下的产品
                    var TIdProductlist = ovProductList.Where(it => it.products.TId == TId).ToList();
                    cartTId.cartList = TIdProductlist;

                    cartAry.cart_Ary_Tids.Add(cartTId);
                    oversea_count++;
                }

                cart_aryList.Add(cartAry);
            }

            int packages = oversea_count > 1 ? 1 : 0; //是否有多个包裹

            #endregion


            #region 登录用户

            //获取用户收货地址
            List<user_address_book> addressList = new List<user_address_book>();
            if (CurrentUserId > 0)
            {
                addressList = await _userAddressBookServices.GetUserAddressBook(CurrentUserId);
            }

            user_address_book defalutAddress = new user_address_book();
            defalutAddress = addressList.Where(it => it.IsDefault).FirstOrDefault();
            if (defalutAddress == null)
            {
                if (addressList.Count > 0)
                {
                    //最新添加的
                    defalutAddress = addressList.OrderByDescending(it => it.UserId).FirstOrDefault();
                }
                else
                {
                    defalutAddress = new user_address_book();
                }
            }
            var TaxThreshold = 0M;
            var Tax = 0M;

            //获取默认国家/省州的税率
            var defalutAddressCountry = await _countryService.GetCountryAsync(defalutAddress.CId ?? 0);
            if (defalutAddressCountry != null)
            {
                Tax = defalutAddressCountry.Tax;
                TaxThreshold = defalutAddressCountry.TaxThreshold;
            }
            var defalutAddressState = await _countryService.GetProvinceAsync(defalutAddress.SId ?? 0);
            if (defalutAddressState != null)
            {
                Tax = defalutAddressState.Tax;
            }

            int point = 0;
            //获取用户
            var user = await _userService.QueryByClauseAsync(it => it.UserId == CurrentUserId && it.Email == CurrentUserEmail);
            if (user != null)
            {
                point = user.Points;
            }

            //获取用户可用优惠券



            #endregion

            #region 收款方式
            var paymethodList = await _paymentService.GetUsedPaymentAsync();
            paymethodList = paymethodList.Where(it => it.PId != 2 && it.IsOnline == true).OrderByDescending(it => it.IsOnline).ToList();

            #endregion

            //checkout链接
            string checkoutUrl = HttpUtility.UrlEncode($"/cart/checkout?data={data}&CId={string.Join(".", idList)}&noMergeCart=1");

            //税费是否包含运费
            var TaxType = await _configService.GetConfigValueByGroup("global", "TaxType");

            var responseData = new
            {
                Countries = countryList, //国家选项
                CartData = r.data,//购物车产品
                AddressList = addressList,//收货地址
                Paymethod = paymethodList,//支付方式
                UserId = CurrentUserId,//用户id
                UserEmail = CurrentUserEmail,//用户币种
                Currency = CurrentCurrency, //返回币种，
                CurrencySymbol = CurrentCurrencySymbol, //返回币种，
                UserSessionId = CurrentUserSessionId,//访客
                Point = point,//积分
                cart_ary = cart_aryList, //仓库层级下的购物车产品
                packages = packages,//是否是多包裹
                CId = data,//购物车id
                DefalutAddress = defalutAddress,//默认收货地址
                checkoutUrl = checkoutUrl,//结算页
                buynow = buynow, //立即购买
                TaxType = TaxType, //税费是否包含运费
                Tax = Tax,//税
                TaxThreshold = TaxThreshold,//起征点
            };

            return View(responseData);
        }

        /// <summary>
        /// 获取订单结算页数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        //[Authorize]
        public async Task<IActionResult> GetOrderConf()
        {
            //国家下拉框
            Dictionary<string, VM_Country> countryDropdown = await _countryService.GetCountryDropdown();

            #region 语言包
            string lang = CurrentLang;
            if (lang.Contains("-"))
            {
                lang = lang.Split('-')[0].ToLower();
            }
            var lang_obj = System.IO.File.ReadAllText(System.IO.Path.Combine(System.Environment.CurrentDirectory, $"Lang/locales/{lang}/translation.json"));
            #endregion

            #region shop_config
            //币种
            var currency_rate = await _currencyService.GetCurrency(CurrentCurrency);

            var TouristsShopping = await _configService.GetConfigByGroup("global", "TouristsShopping");
            var CheckoutMode = await _configService.GetConfigByGroup("global", "CheckoutMode");

            string ip = Request.GetIP();
            //获取ip的国家
            var ipcountry = _commonService.GetAliyunCountryCode(ip);

            //获取用户默认收货地址
            //获取用户收货地址
            int AId = -1;
            List<user_address_book> addressList = new List<user_address_book>();
            if (CurrentUserId > 0)
            {
                addressList = await _userAddressBookServices.GetUserAddressBook(CurrentUserId);
            }
            user_address_book defalutAddress = new user_address_book();
            defalutAddress = addressList.Where(it => it.IsDefault).FirstOrDefault();
            if (defalutAddress == null)
            {
                if (addressList.Count > 0)
                {
                    //最新添加的
                    defalutAddress = addressList.OrderByDescending(it => it.UserId).FirstOrDefault();
                    AId = defalutAddress.AId;
                }
            }

            Dictionary<string, object> shop_config = new Dictionary<string, object>
            {
                {"domain" ,AppSettingsConstVars.WebSiteUrl},
                {"date",DateTime.Now.ToString() },
                {"lang",CurrentLang },
                {"language",CurrentLang },
                {"language_name",  GlobalConstVars.lang_name.ContainsKey(CurrentLang)? GlobalConstVars.lang_name[CurrentLang] :CurrentLang },
                {"currency",CurrentCurrency },
                {"currency_symbols",CurrentCurrencySymbol },
                {"currency_rate",(currency_rate!=null ? currency_rate.Rate.Value:1.0000m).ToString()  },//--
                {"UserId",CurrentUserId.ToString() },
                {"TouristsShopping",TouristsShopping!=null ?TouristsShopping.Value:"0" },//
                {"tmp_dir","/tmp/" },
                {"IsMobile",0},
                {"pintrkPixelOpen",0 },
                {"IsViewPro","0" },
                {"SnapchatPixelOpen","0" },
                {"KwaiPixelOpen","0" },
                {"CheckoutMode",CheckoutMode!=null ? CheckoutMode.Value:"0" },//---
                {"PlusPurchaseMethod","popup_right" },
                {"ipCountry",ipcountry }, //ip国家
                {"isPriceHide",0 },
                {"isProductsPriceCrypt",0 },
                {"userDiscount",1 },
                {"left","left" },
                {"right","right"},
                {"AId",AId }
            };

            #endregion

            #region memberPointsConfig  earnPointsOrderConfig


            Dictionary<string, object> memberPointsConfig = new Dictionary<string, object>();


            Dictionary<string, object> earnPointsOrderConfig = new Dictionary<string, object>();

            var mConfig = await _configService.GetConfigByGroup("member_points", "config");
            if (mConfig != null)
            {
                Dictionary<string, Dictionary<string, object>> mConfigValue = mConfig.Value.JsonToObj<Dictionary<string, Dictionary<string, object>>>();
                bool isUsed = false;
                if (mConfigValue != null && mConfigValue.ContainsKey("order"))
                {
                    if (mConfigValue["order"].ContainsKey("isUsed"))
                    {
                        isUsed = mConfigValue["order"]["isUsed"].ToString() == "1";
                    }
                }
                if (isUsed)
                {
                    earnPointsOrderConfig = mConfigValue["order"];
                }

                var memberPointList = await _memberPointsService.GetMemberPointsAsync();
                var memberPoint = memberPointList.Where(it => it.Method == "shopping").FirstOrDefault();
                if (memberPoint != null)
                {
                    memberPointsConfig = memberPoint.ToJson().JsonToObj<Dictionary<string, object>>();
                    if (CurrentUserId > 0)
                    {
                        //获取用户
                        var user = await _userService.QueryByClauseAsync(it => it.UserId == CurrentUserId && it.Email == CurrentUserEmail);
                        if (user != null)
                        {
                            memberPointsConfig.Add("MemberPoints", user.Points);
                        }
                        else
                        {
                            memberPointsConfig.Add("MemberPoints", 0);
                        }
                    }

                }

            }

            #endregion

            #region AddressDefaultInfo

            Dictionary<string, object> AddressDefaultInfo = new Dictionary<string, object>();

            var shoppingConfig = await _configService.GetConfigByGroup("shopping", "ShippingAddress");
            if (shoppingConfig != null)
            {
                var shoppingConfigDir = shoppingConfig.Value.JsonToObj<Dictionary<string, List<string>>>();
                if (shoppingConfigDir.ContainsKey("labels"))
                {
                    Dictionary<string, string> labelDir = new Dictionary<string, string>();
                    foreach (var label in shoppingConfigDir["labels"])
                    {
                        labelDir.Add(label, _localizer[$"address.shipping.{label}"].Value);
                    }
                    AddressDefaultInfo.Add("labels", labelDir);
                }

                if (shoppingConfigDir.ContainsKey("address_format"))
                {
                    AddressDefaultInfo.Add("address_format", shoppingConfigDir["address_format"]);
                }
            }

            //获取地址附加字段
            Dictionary<string, Dictionary<string, string>> addressExpDic = new Dictionary<string, Dictionary<string, string>>();
            var addressExp = await _countryService.GetAddressExpandAsync();
            addressExp = addressExp.Where(it => it.Scope == "global" && it.IsOpen).ToList();
            if (addressExp.Count > 0)
            {

                foreach (var address in addressExp)
                {
                    addressExpDic.Add(address.Param, address.Data.JsonToObj<Dictionary<string, string>>());
                }
                AddressDefaultInfo.Add("attributes", addressExpDic);
            }
            else
            {
                AddressDefaultInfo.Add("attributes", addressExpDic);
            }

            #endregion


            var responseData = new
            {
                lang_obj = lang_obj, //语言包
                shop_config = shop_config, //
                memberPointsConfig = memberPointsConfig,//获取积分成功下单的配置
                earnPointsOrderConfig = earnPointsOrderConfig, //获取积分成功下单的配置
                addressDefaultInfo = AddressDefaultInfo,//收货地址配置项
                countries = countryDropdown,//国家下拉框
            };

            return Json(responseData.ToJson());
        }


        /// <summary>
        /// 获取国家下拉框
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        //[Authorize]
        public async Task<IActionResult> GetCountries()
        {
            Dictionary<string, VM_Country> countryDropdown = await _countryService.GetCountryDropdown();

            return Json(countryDropdown);
        }

        /// <summary>
        /// 查看购物车
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("/cart")]
        //[Authorize]
        public async Task<IActionResult> Cart()
        {

            var cartData = new WebApiCallBack<CartDto>();

            //获取用户币种
            var currency = await _currencyService.GetCurrency(CurrentCurrency);

            //获取后台币种
            var defaultCurrency = await _currencyService.GetManageDefaultCurrency();

            #region 更新购物
            if (CurrentUserId > 0)
            {
                await _cartServices.UpdateCart(CurrentUserId);

            }
            else
            {
                await _cartServices.UpdateCart(CurrentUserSessionId);
            }
            #endregion


            if (CurrentUserId > 0)//在线客户
            {
                cartData = await _cartServices.GetCartInfos(CurrentUserId, currency, defaultCurrency);
            }
            else
            {
                cartData = await _cartServices.GetCartInfos(CurrentUserSessionId, currency, defaultCurrency);
            }


            //var jm = new WebApiCallBack<CartDto>() { methodDescription = "获取处理后的购物车信息" };
            //if (cartData.status)
            //{



            //    //foreach (var item in cartData.data.list)
            //    //{
            //    //    item.oldPriceFormat = _currencyService.ShowPriceFormat(item.oldPrice, currency, defaultCurrency);
            //    //    item.priceFormat = _currencyService.ShowPriceFormat(item.price, currency, defaultCurrency);
            //    //    item.itemTotalFormat = _currencyService.ShowPriceFormat(item.price * item.nums, currency, defaultCurrency);
            //    //}

            //    //cartData.data.goodsAmountFormat = _currencyService.ShowPriceFormat(cartData.data.goodsAmount, currency, defaultCurrency); //商品总价
            //    //cartData.data.amountFormat = _currencyService.ShowPriceFormat(cartData.data.amount, currency, defaultCurrency);//订单总计
            //    //cartData.data.orderPromotionMoneyFormat = _currencyService.ShowPriceFormat(cartData.data.orderPromotionMoney, currency, defaultCurrency);//优惠总计

            //    cartData.data.list = cartData.data.list.OrderByDescending(it => it.createTime).OrderBy(it => it.isInvalid).ToList();
            //    cartData.data.failureList = cartData.data.list.Where(it => it.isInvalid).ToList();
            //    cartData.data.ProductList = cartData.data.list.Where(it => it.isInvalid == false).ToList();

            //    //jm.data = r.data;
            //}



            return View(cartData.data);
        }


        [HttpGet]
        [Route("/cart/drawer")]
        //[Authorize]
        public async Task<IActionResult> MiniCartDrawer()
        {

            var cartData = new WebApiCallBack<CartDto>();

            //获取用户币种
            var currency = await _currencyService.GetCurrency(CurrentCurrency);

            //获取后台币种
            var defaultCurrency = await _currencyService.GetManageDefaultCurrency();



            #region 更新购物
            if (CurrentUserId > 0)
            {
                await _cartServices.UpdateCart(CurrentUserId);

            }
            else
            {
                await _cartServices.UpdateCart(CurrentUserSessionId);
            }
            #endregion


            if (CurrentUserId > 0)//在线客户
            {
                cartData = await _cartServices.GetCartInfos(CurrentUserId, currency, defaultCurrency);
            }
            else
            {
                cartData = await _cartServices.GetCartInfos(CurrentUserSessionId, currency, defaultCurrency);
            }


            //var jm = new WebApiCallBack<CartDto>() { methodDescription = "获取处理后的购物车信息" };
            if (cartData.status)
            {
                ////获取用户币种
                //var currency = await _currencyService.GetCurrency(CurrentCurrency);

                ////获取后台币种
                //var defaultCurrency = await _currencyService.GetManageDefaultCurrency();


                //foreach (var item in cartData.data.list)
                //{
                //    item.oldPriceFormat = _currencyService.ShowPriceFormat(item.oldPrice, currency, defaultCurrency);
                //    item.priceFormat = _currencyService.ShowPriceFormat(item.price, currency, defaultCurrency);
                //    item.itemTotalFormat = _currencyService.ShowPriceFormat(item.price * item.nums, currency, defaultCurrency);
                //}

                //cartData.data.goodsAmountFormat = _currencyService.ShowPriceFormat(cartData.data.goodsAmount, currency, defaultCurrency); //商品总价
                //cartData.data.amountFormat = _currencyService.ShowPriceFormat(cartData.data.amount, currency, defaultCurrency);//订单总计
                //cartData.data.orderPromotionMoneyFormat = _currencyService.ShowPriceFormat(cartData.data.orderPromotionMoney, currency, defaultCurrency);//优惠总计

                cartData.data.list = cartData.data.list.OrderByDescending(it => it.createTime).OrderBy(it => it.isInvalid).ToList();
                cartData.data.failureList = cartData.data.list.Where(it => it.isInvalid).ToList();
                cartData.data.ProductList = cartData.data.list.Where(it => it.isInvalid == false).ToList();

                //jm.data = r.data;
            }



            return PartialView(cartData.data);
        }



        /// <summary>
        /// 验证支付方式
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        //[Authorize]
        public async Task<IActionResult> CheckPaymentMethods()
        {
            int CId = Request.GetFormInt("CId"); //国家id
            string methodSId = Request.GetFormString("methodSId");
            if (CId == 0)
            {
                return Ok(new { ret = 0, msg = "" });
            }

            List<int> data = new List<int>();

            //获取启用的支付方式
            var payment = await _paymentService.GetUsedPaymentAsync();
            foreach (var item in payment)
            {
                if (!item.Country.IsNullOrEmpty() && item.Country != "[0]")
                {
                    var countryData = item.Country.JsonToObj<List<int>>();
                    if (countryData != null && countryData.Contains(CId))
                    {
                        data.Add(item.PId);
                    }
                }
            }


            return Ok(new { ret = 1, msg = new { @out = data } });

        }

        /// <summary>
        /// 获取邮件方式
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        //[Authorize]
        public async Task<IActionResult> GetShippingMethods()
        {
            int CId = Request.GetFormInt("CId"); //国家id
            string order_cid = Request.GetFormString("order_cid");//购物车产品id
            int IsBuyNow = Request.GetFormInt("IsBuyNow");
            string ZipCode = Request.GetFormString("ZipCode");
            int StatesSId = Request.GetFormInt("StatesSId");

            ShippingMethodRequest shippingRequest = new ShippingMethodRequest
            {
                CId = CId,
                OrderCId = HttpUtility.UrlDecode(order_cid.FromBase64()),
                IsBuyNow = IsBuyNow,
                StatesSId = StatesSId,
                CurrentCurrency = CurrentCurrency,

            };

            //获取邮寄方式
            var shippingResult = await _shippingMethodThirdService.GetShippingMethodsAsync(shippingRequest);
            if (shippingResult.ret == 0)
            {
                return Ok(new { ret = 2, msg = _localizer["web.tracking.no_tracking"].Value });
            }



            return Content(shippingResult.ToJson());

        }


        /// <summary>
        /// 获取收货地址
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        //[Authorize]
        public async Task<IActionResult> GetAddressBook()
        {
            int AId = Request.GetFormInt("AId");
            int NotUser = Request.GetFormInt("NotUser");
            int isSelect = Request.GetFormInt("isSelect");
            string virtualStatus = Request.GetFormString("virtualStatus");

            //收货地址
            Dictionary<string, string> address_row = new Dictionary<string, string>();

            //国家
            Dictionary<string, object> country_row = new Dictionary<string, object>();

            //省/州
            Dictionary<string, object> stateAry = new Dictionary<string, object>();

            //获取国家列表
            var countryList = await _countryService.GetCountriesAsync();
            //获取省州
            var stateList = await _countryService.GetCountryStatesAsync();

            //收货地址
            var address = await _userAddressBookServices.QueryByClauseAsync(it => it.AId == AId && it.UserId == CurrentUserId);
            if (address == null)
            {
                address = new user_address_book();
            }


            //国家
            var country = countryList.Where(it => it.CId == address.CId).FirstOrDefault();
            if (country != null)
            {
                string countryName = _localizer[$"area.country.{country.Acronym}"].Value;

                country_row.Add("Country", countryName);
            }
            else
            {
                country = countryList.Where(it => it.IsUsed == true).FirstOrDefault();
                if (country != null)
                {
                    string countryName = _localizer[$"area.country.{country.Acronym}"].Value;

                    country_row.Add("Country", countryName);
                }
                else
                {
                    country = new country();
                    country_row.Add("Country", "");
                }
            }

            if (address.AId == 0)
            {

                address.CountryCode = country.Code;
                address.Country = country.Country;


            }

            address_row = address.ToJson().JsonToObj<Dictionary<string, string>>();
            if (!address_row.ContainsKey("CountryCode"))
            {
                address_row.Add("CountryCode", address.CountryCode.ToString());
            }
            //省州
            var state = stateList.Where(it => it.country_code == country.Acronym).OrderBy(it => it.States).ToList();

            country_row.Add("HasState", state.Count > 0 ? 1 : 0);

            stateAry.Add(country.CId.ToString(), state);

            Dictionary<string, string> countryCodeAry = new Dictionary<string, string>
            {
                {country.CId.ToString(), country.Code.ToString()}
            };

            var result = new
            {
                address = address_row,
                country = country_row,
                stateAry = stateAry,
                countryCodeAry = countryCodeAry,
            };

            return Ok(new { ret = CurrentUserId > 0 ? 1 : 2, msg = result });
        }


        /// <summary>
        /// 获取收货地址
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        //[Authorize]
        public async Task<IActionResult> GetBillingAddress()
        {

            //收货地址
            Dictionary<string, string> address_row = new Dictionary<string, string>();

            //国家
            Dictionary<string, object> country_row = new Dictionary<string, object>();

            //省/州
            Dictionary<string, object> stateAry = new Dictionary<string, object>();

            //获取国家列表
            var countryList = await _countryService.GetCountriesAsync();
            //获取省州
            var stateList = await _countryService.GetCountryStatesAsync();

            //收货地址
            var address = await _userAddressBookServices.QueryByClauseAsync(it => it.IsBillingAddress == true && it.UserId == CurrentUserId);
            if (address == null)
            {
                address = new user_address_book();
            }


            //国家
            var country = countryList.Where(it => it.CId == address.CId).FirstOrDefault();
            if (country != null)
            {
                string countryName = _localizer[$"area.country.{country.Acronym}"].Value;

                country_row.Add("Country", countryName);
            }
            else
            {
                country = countryList.Where(it => it.IsUsed == true).FirstOrDefault();
                if (country != null)
                {
                    string countryName = _localizer[$"area.country.{country.Acronym}"].Value;

                    country_row.Add("Country", countryName);
                }
                else
                {
                    country = new country();
                    country_row.Add("Country", "");
                }
            }

            if (address.AId == 0)
            {
                address.CountryCode = country.Code;
                address.Country = country.Country;

            }

            address_row = address.ToJson().JsonToObj<Dictionary<string, string>>();
            if (!address_row.ContainsKey("CountryCode"))
            {
                address_row.Add("CountryCode", address.CountryCode.ToString());
            }
            //省州
            var state = stateList.Where(it => it.country_code == country.Acronym).OrderBy(it => it.States).ToList();

            country_row.Add("HasState", state.Count > 0 ? 1 : 0);

            stateAry.Add(country.CId.ToString(), state);

            Dictionary<string, string> countryCodeAry = new Dictionary<string, string>
            {
                {country.CId.ToString(), country.Code.ToString()}
            };

            var result = new
            {
                address = address_row,
                country = country_row,
                stateAry = stateAry,
                countryCodeAry = countryCodeAry,
            };

            return Ok(new { ret = CurrentUserId > 0 ? 1 : 2, msg = result });
        }

        /// <summary>
        /// 创建订单
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        //[Authorize]
        public async Task<IActionResult> CreateOrder()
        {
            #region 参数
            //            {
            //                "0": "BillFirstName=zhang",
            //    "1": "BillLastName=222",
            //    "2": "BillAddressLine1=fff",
            //    "3": "BillAddressLine2=fff",
            //    "4": "BillCity=ff",
            //    "5": "Billcountry_id=13",
            //    "6": "BillProvince=4",
            //    "7": "BillZipCode=2233",
            //    "8": "BillCountryCode=%2B61",
            //    "9": "BillPhoneNumber=feeeee",
            //    "10": "BilltypeAddr=1",
            //    "11": "Billedit_address_id=",
            //    "order_coupon_code": "",
            //    "order_discount_price": "0",
            //    "order_shipping_address_aid": "-1",
            //    "order_shipping_address_cid": "13",
            //    "order_shipping_address_sid": "",
            //    "order_shipping_method_sid": "[]",
            //    "order_shipping_method_type": "[]",
            //    "order_shipping_price": "[]",
            //    "order_shipping_oversea": "0-0",
            //    "order_payment_method_pid": "154",
            //    "order_cid": "93��",
            //    "IsBuyNow": "0",
            //    "checkoutStep": "contact_information",
            //    "billAddressType": "different",
            //    "OrdersPoints": "0",
            //    "FirstName": "fs",
            //    "LastName": "fds",
            //    "AddressLine1": "1122",
            //    "AddressLine2": "33",
            //    "City": "444",
            //    "country_id": "13",
            //    "Province": "2",
            //    "ZipCode": "2323",
            //    "CountryCode": "+61",
            //    "PhoneNumber": "3122",
            //    "typeAddr": 1,
            //    "edit_address_id": "",
            //    "Email": "",
            //    "Comments": "22333"
            //} 
            #endregion


            // 收货地址信息
            string firstName = Request.GetFormString("FirstName");
            string lastName = Request.GetFormString("LastName");
            string addressLine1 = Request.GetFormString("AddressLine1");
            string addressLine2 = Request.GetFormString("AddressLine2");
            string city = Request.GetFormString("City");
            int countryId = Request.GetFormInt("country_id");
            int province = Request.GetFormInt("Province");
            string zipCode = Request.GetFormString("ZipCode");
            string countryCode = Request.GetFormString("CountryCode");
            string phoneNumber = Request.GetFormString("PhoneNumber");
            string typeAddr = Request.GetFormString("typeAddr");
            int editAddressId = Request.GetFormInt("edit_address_id");

            // 订单信息
            string orderCouponCode = Request.GetFormString("order_coupon_code"); //优惠券P7ASQVWU
            string orderDiscountPrice = Request.GetFormString("order_discount_price");
            string orderShippingAddressAid = Request.GetFormString("order_shipping_address_aid");
            string orderShippingAddressCid = Request.GetFormString("order_shipping_address_cid");
            string orderShippingAddressSid = Request.GetFormString("order_shipping_address_sid");
            string orderShippingMethodSid = Request.GetFormString("order_shipping_method_sid");//发货方式
            string orderShippingMethodType = Request.GetFormString("order_shipping_method_type");//海运或空运
            string orderShippingPrice = Request.GetFormString("order_shipping_price");
            string orderShippingOversea = Request.GetFormString("order_shipping_oversea");//发货方式 , 逗号拼接
            string orderPaymentMethodPid = Request.GetFormString("order_payment_method_pid");//支付方式
            string orderCid = Request.GetFormString("order_cid");
            int isBuyNow = Request.GetFormInt("IsBuyNow");
            string checkoutStep = Request.GetFormString("checkoutStep");
            string billAddressType = Request.GetFormString("billAddressType");
            int ordersPoints = Request.GetFormInt("OrdersPoints");//积分
            string paymethod = Request.GetFormString("paymethod");//支付方式

            string email = Request.GetFormString("Email");//邮箱
            string comments = Request.GetFormString("Comments");//备注
            byte buyerNewsletter = Request.GetFormByte("buyerNewsletter");//订阅


            //获取购物车id
            //string idparam = HttpUtility.UrlEncode(CIdAry).ToBase64();

            string idparam = HttpUtility.UrlDecode(orderCid.FromBase64());
            if (idparam.IsNullOrEmpty())
            {
                return Redirect("/cart");
            }
            List<int> idList = new List<int>();
            List<string> ids = idparam.Split(',').ToList();
            if (ids.Count == 0)
            {
                return Redirect("/cart");
            }
            foreach (var id in ids)
            {
                var s = int.TryParse(id, out int CId);
                if (s == false || CId == 0)
                {
                    return Redirect("/cart");
                }
                idList.Add(CId);
            }

            //获取选中的商品
            var cartResp = new WebApiCallBack<CartDto>();

            //获取用户币种
            var currency = await _currencyService.GetCurrency(CurrentCurrency);

            //获取后台币种
            var defaultCurrency = await _currencyService.GetManageDefaultCurrency();



            //获取购物车
            if (CurrentUserId > 0)//在线客户
            {
                cartResp = await _cartServices.GetCartInfos(CurrentUserId, currency, defaultCurrency, idList);
            }
            else
            {
                cartResp = await _cartServices.GetCartInfos(CurrentUserSessionId, currency, defaultCurrency, idList);
            }

            if (cartResp.status == false)
            {
                return Ok(new { ret = 0, msg = _localizer[cartResp.msg].Value });
            }

            ////获取用户币种
            //var currency = await _currencyService.GetCurrency(CurrentCurrency);

            ////获取后台币种
            //var defaultCurrency = await _currencyService.GetManageDefaultCurrency();

            cartResp.data.currency = currency;
            cartResp.data.defaultCurrency = defaultCurrency;

            //收货地址
            user_address_book shipAddress = new user_address_book
            {
                AId = editAddressId,
                UserId = CurrentUserId,
                FirstName = firstName,
                LastName = lastName,
                AddressLine1 = addressLine1,
                AddressLine2 = addressLine2,
                City = city,
                SId = province,
                CId = countryId,
                CodeOption = (sbyte)0,
                TaxCode = "",
                ZipCode = zipCode,
                PhoneNumber = phoneNumber,
            };

            if (editAddressId > 0 && CurrentUserId > 0)
            {
                //更新用户收货地址
                var shipAddr = await _userAddressBookServices.QueryByClauseAsync(it => it.AId == editAddressId && it.UserId == CurrentUserId);
                if (shipAddr != null)
                {
                    shipAddr.FirstName = firstName;
                    shipAddr.LastName = lastName;
                    shipAddr.AddressLine1 = addressLine1;
                    shipAddr.AddressLine2 = addressLine2;
                    shipAddr.City = city;
                    shipAddr.SId = province;
                    shipAddr.CId = countryId;
                    shipAddr.ZipCode = zipCode;
                    shipAddr.PhoneNumber = phoneNumber;
                    await _userAddressBookServices.Update(shipAddr);
                }

            }


            //账单地址
            user_address_book billAddress = new user_address_book();
            if (billAddressType == "different")
            {
                Dictionary<string, string> billData = new Dictionary<string, string>();

                // 假设 Request 包含多个参数：0, 1, 2, ..., N
                for (int i = 0; i < 12; i++) // 假设最多 12 个参数（0-11）
                {
                    string paramValue = Request.GetFormString(i.ToString());
                    if (!string.IsNullOrEmpty(paramValue))
                    {
                        string[] parts = paramValue.Split('=');
                        if (parts.Length == 2)
                        {
                            switch (parts[0])
                            {
                                case "Billcountry_id":
                                    {
                                        if (parts[1].IsNullOrEmpty())
                                        {
                                            billData["CId"] = "0";
                                        }
                                        else
                                        {
                                            billData["CId"] = parts[1];
                                        }
                                        break;
                                    }
                                case "BillProvince":
                                    {
                                        if (parts[1].IsNullOrEmpty())
                                        {
                                            billData["SId"] = "0";
                                        }
                                        else
                                        {
                                            billData["SId"] = parts[1];
                                        }
                                        break;
                                    }
                                case "Billedit_address_id":
                                    {
                                        if (parts[1].IsNullOrEmpty())
                                        {
                                            billData["AId"] = "0";
                                        }
                                        else
                                        {
                                            billData["AId"] = parts[1];
                                        }
                                        break;
                                    }
                                case "BilltypeAddr":
                                    {
                                        break;
                                    }
                                case "BillCountryCode":
                                    {
                                        billData[parts[0].Trim().Replace("Bill", "")] = parts[1].TrimStart('+').Replace("%2B", "");
                                        break;
                                    }

                                default:
                                    {
                                        billData[parts[0].Trim().Replace("Bill", "")] = parts[1];
                                        break;
                                    }
                            }


                        }
                    }
                }

                billAddress = billData.ToJson().JsonToObj<user_address_book>();

                if (billAddress.AId > 0 && CurrentUserId > 0)
                {
                    //更新用户账单地址
                    var shipAddr = await _userAddressBookServices.QueryByClauseAsync(it => it.AId == editAddressId && it.UserId == CurrentUserId && it.IsBillingAddress == true);
                    if (shipAddr != null)
                    {
                        shipAddr.FirstName = billAddress.FirstName;
                        shipAddr.LastName = billAddress.LastName;
                        shipAddr.AddressLine1 = billAddress.AddressLine1;
                        shipAddr.AddressLine2 = billAddress.AddressLine2;
                        shipAddr.City = billAddress.City;
                        shipAddr.SId = billAddress.SId;
                        shipAddr.CId = billAddress.CId;
                        shipAddr.ZipCode = billAddress.ZipCode;
                        shipAddr.PhoneNumber = billAddress.PhoneNumber;
                        await _userAddressBookServices.Update(shipAddr);
                    }
                }

            }
            else if (billAddressType == "same")
            {
                billAddress = shipAddress.MapTo<user_address_book>();
            }

            //获取邮寄方式
            ShippingMethodRequest shippingRequest = new ShippingMethodRequest
            {
                CId = countryId,
                OrderCId = idparam,
                IsBuyNow = isBuyNow,
                StatesSId = province,
                CurrentCurrency = CurrentCurrency,


            };
            //获取邮寄方式
            var shippingResult = await _shippingMethodThirdService.GetShippingMethodsAsync(shippingRequest);
            if (shippingResult.ret == 0)
            {
                return Ok(new { ret = 0, msg = _localizer["web.tracking.no_tracking"].Value });
            }
            //获取运费
            Dictionary<string, ShippingMethodItem> shippingMethodSid = new Dictionary<string, ShippingMethodItem>();
            //List<string> orderShippingOverseaList = orderShippingOversea.Split(",").ToList();
            //if (orderShippingOverseaList.Count == 0)
            //{
            //    return Ok(new { ret = 0, msg = _localizer["web.tracking.no_tracking"] });
            //}
            if (orderShippingMethodSid.IsNullOrEmpty())
            {
                return Ok(new { ret = 0, msg = _localizer["web.tracking.no_tracking"].Value });
            }

            var SidDic = orderShippingMethodSid.JsonToObj<Dictionary<string, int>>();


            foreach (var shipping in SidDic)
            {
                int SId = shipping.Value;
                if (!shippingResult.msg.info.ContainsKey(shipping.Key))
                {
                    return Ok(new { ret = 0, msg = _localizer["web.tracking.no_tracking"].Value });
                }

                var shipModel = shippingResult.msg.info[shipping.Key].Where(it => it.SId == SId).FirstOrDefault();
                if (shipModel == null)
                {
                    return Ok(new { ret = 0, msg = _localizer["web.tracking.no_tracking"].Value });
                }

                shippingMethodSid.Add(shipping.Key, shipModel);
            }

            //运费
            cartResp.data.costFreight = shippingMethodSid.Values.Sum(it => it.webShippingPrice);
            cartResp.data.amount += cartResp.data.costFreight;

            string ip = Request.GetIP();

            user user = new user();
            if (CurrentUserId == 0)
            {
                //创建访客用户
                user = await _userService.GetOrCreateUser(email, ip, shipAddress, CurrentLang, CurrentCurrency, CurrentSalesAreaCode, buyerNewsletter);
            }
            else
            {
                //获取用户信息
                user = await _userService.QueryByClauseAsync(it => it.UserId == CurrentUserId && it.Email == CurrentUserEmail);
            }

            CouponInfo coupon = new CouponInfo();

            //获取优惠券结果
            if (!orderCouponCode.IsNullOrEmpty())
            {
                //获取优惠券结果
                var couponRes = await _salesCouponService.CheckCouponAsync(orderCouponCode, cartResp.data, cartResp.data.goodsAmount, cartResp.data.orderPromotionMoney, pOrderCid: idparam, userId: CurrentUserId, sessionId: CurrentUserSessionId, currency: CurrentCurrency);
                if (couponRes.Item1 && couponRes.Item4.Status == 1)//优惠券可用
                {
                    coupon = couponRes.Item4;

                    cartResp.data.orderDiscount = 0; //折扣
                    cartResp.data.orderPromotionMoney = 0;//减金额

                    cartResp.data.couponPromotionDiscount = couponRes.Item4.Discount; //折扣
                    cartResp.data.couponPromotionMoney = couponRes.Item2;//减金额

                    cartResp.data.coupon = new List<string> { orderCouponCode };
                    //cartResp.data.amount=
                }
                else
                {
                    coupon.CouponCode = orderCouponCode;
                }
            }


            #region 获取支付方式

            //获取支付方式
            var payment = await _paymentService.GetUsedPaymentAsync(paymethod);
            if (payment == null)
            {
                //支付方式不存在
                return Json(new { ret = 0, msg = "" });
            }

            #endregion

            //创建订单
            var createResult = await _orderService.CreateOrder(user, cartResp.data, shipAddress, billAddress, point: ordersPoints, payment: payment, coupon, shippingMethodSid, comments, CurrentCurrency, ip);
            if (createResult.status)
            {
                if (CurrentUserId > 0)//在线客户
                {

                    //获取购物车数量
                    int count = await _cartServices.GetCountAsync(CurrentUserId);

                    //更新jwt Token
                    await SetTokenCartCount(count);

                }
                else
                {
                    //获取购物车数量
                    int count = await _cartServices.GetCountAsync(CurrentUserSessionId);

                    //设置session
                    CurrentCartCount = count;
                }
            }

            //如果创建订单成功，则返回订单号
            return Ok(new
            {
                ret = createResult.status ? 1 : 0,
                msg = new
                {
                    oId = createResult.status ? createResult.data.OId : "",
                    isUpdate = false,
                    paymethod = createResult.status ? createResult.data.PaymentMethod : "",
                }
            });

        }

        public IActionResult PaymentInfo()
        {
            return View();
        }

        public IActionResult Shipping()
        {
            return View();
        }

        /// <summary>
        /// 支付完成页面
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult Complete(string orderId)
        {

            ViewData["orderId"] = orderId;
            return View();
        }

        /// <summary>
        /// 取消支付页面
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult Cancel(string orderId)
        {
            ViewData["orderId"] = orderId;
            return View();
        }

        /// <summary>
        /// 购物车成功页面
        /// </summary>
        /// <returns></returns>
        [Route("/cart/{OId}/Info")]
        public async Task<IActionResult> OrderInfo(string OId)
        {

            string code = Request.GetQueryString("code");
            if (code.IsNullOrEmpty() || OId.IsNullOrEmpty())
            {
                return Redirect("/");
            }

            if (OId != code.FromBase64())
            {
                return Redirect("/");
            }

            // 获取订单详情
            var order = await _orderService.QueryByClauseAsync(it => it.OId == OId);
            if (order == null)
            {
                return Redirect("/");
            }

            if (order.PaymentStatus != "unpaid") //已付款
            {
                return Redirect("/");

            }

            //获取订单商品列表
            var orderProducts = await _orderProductsService.Query(it => it.OrderId == order.OrderId);
            if (orderProducts.Count == 0)
            {
                return Redirect("/");
            }

            List<int> productId = orderProducts.Select(it => it.ProId.Value).ToList();

            //产品列表
            var productList = await _productService.Query(it => productId.Contains(it.ProId));


            //获取所有仓库
            var overList = await _shippingWarehouseService.GetAllWarehousesAsync();

            //获取币种
            var currency = await _currencyService.GetCurrency(order.Currency);

            //获取后台币种
            var defaultCurrency = await _currencyService.GetManageDefaultCurrency();


            List<VM_OrderProduct> orderProduct = new List<VM_OrderProduct>();
            foreach (var item in orderProducts)
            {
                VM_OrderProduct model = item.MapTo<VM_OrderProduct>();
                model.priceFormat = _currencyService.ShowPriceFormat(item.Price.Value, currency, defaultCurrency);
                model.itemTotalFormat = _currencyService.ShowPriceFormat(item.Price.Value * item.Qty.Value, currency, defaultCurrency);

                var product = productList.Where(it => it.ProId == item.ProId).FirstOrDefault();
                if (product != null)
                {
                    model.product = product;
                }

                //获取仓库
                var overseas = overList.Where(it => it.OvId == item.OvId).FirstOrDefault();
                if (overseas != null)
                {
                    model.Overseas = overseas.Name;
                }

                orderProduct.Add(model);
            }


            //购物车详情
            //var cartDto = await _cartServices.GetCartInfos(orderProducts);


            //foreach (var item in cartDto.data.list)
            //{
            //    item.oldPriceFormat = _currencyService.ShowPriceFormat(item.oldPrice, currency, defaultCurrency);
            //    item.priceFormat = _currencyService.ShowPriceFormat(item.price, currency, defaultCurrency);
            //    item.itemTotalFormat = _currencyService.ShowPriceFormat(item.price * item.nums, currency, defaultCurrency);
            //}

            //cartDto.data.goodsAmountFormat = _currencyService.ShowPriceFormat(cartDto.data.goodsAmount, currency, defaultCurrency); //商品总价
            //cartDto.data.amountFormat = _currencyService.ShowPriceFormat(cartDto.data.amount, currency, defaultCurrency);//订单总计
            //cartDto.data.orderPromotionMoneyFormat = _currencyService.ShowPriceFormat(cartDto.data.orderPromotionMoney, currency, defaultCurrency);//优惠总计
            //cartDto.data.costFreight = order.ShippingPrice ?? 0;
            //cartDto.data.costFreightFormat = _currencyService.ShowPriceFormat(cartDto.data.costFreight, currency, defaultCurrency);

            //cartDto.data.list = cartDto.data.list.OrderByDescending(it => it.createTime).OrderBy(it => it.isInvalid).ToList();


            #region 收款方式

            var paymethodList = await _paymentService.GetUsedPaymentAsync();
            paymethodList = paymethodList.Where(it => it.PId != 2 && it.IsOnline == true).OrderByDescending(it => it.IsOnline).ToList();

            #endregion


            //收货地址
            var shipping = new { Email = order.Email, FirstName = order.ShippingFirstName, LastName = order.ShippingLastName, PhoneNumber = order.ShippingPhoneNumber, City = order.ShippingCity, Country = order.ShippingCountry, CountryAcronym = "", AddressLine1 = order.ShippingAddressLine1, AddressLine2 = order.ShippingAddressLine2, ZipCode = order.ShippingZipCode, State = order.ShippingState };

            //转换后的运费
            var currencyShipPrice = _currencyService.ShowPriceFormat(order.ShippingPrice.Value, currency, defaultCurrency);

            /// <summary>
            /// 订单价格明细
            /// </summary>
            (decimal ProductPrice, decimal DiscountPrice, decimal CouponPrice, decimal PointsPrice, decimal ShippingPrice,
             decimal FeePrice, decimal TaxPrice, decimal TotalPrice) ordersPriceAry = await _orderListService.OrdersDetailPrice(order, 1, 0, false, true, new ReplaceDataModel() { shippingPrice = currencyShipPrice.Item1 });

            ordersPriceAry.TotalPrice = ordersPriceAry.TotalPrice - ordersPriceAry.FeePrice;

            var ordersPrice = new
            {
                ProductPrice = ordersPriceAry.ProductPrice,
                DiscountPrice = ordersPriceAry.DiscountPrice,
                CouponPrice = ordersPriceAry.CouponPrice,
                PointsPrice = ordersPriceAry.PointsPrice,
                ShippingPrice = ordersPriceAry.ShippingPrice,
                FeePrice = ordersPriceAry.FeePrice,
                TaxPrice = ordersPriceAry.TaxPrice,
                TotalPrice = ordersPriceAry.TotalPrice,
            };

            var ordersPriceFormat = new
            {
                ProductPrice = _helpsCartService.CurrencyFormat(ordersPrice.ProductPrice, order.Currency),
                DiscountPrice = _helpsCartService.CurrencyFormat(ordersPrice.DiscountPrice, order.Currency),

                CouponPrice = _helpsCartService.CurrencyFormat(ordersPrice.CouponPrice, order.Currency),
                PointsPrice = _helpsCartService.CurrencyFormat(ordersPrice.PointsPrice, order.Currency),
                ShippingPrice = _helpsCartService.CurrencyFormat(ordersPrice.ShippingPrice, order.Currency),
                FeePrice = _helpsCartService.CurrencyFormat(ordersPrice.FeePrice, order.Currency),

                TaxPrice = _helpsCartService.CurrencyFormat(ordersPrice.TaxPrice, order.Currency),
                TotalPrice = _helpsCartService.CurrencyFormat(ordersPrice.TotalPrice, order.Currency),
            };



            //设置取消支付的原因，解决方案
            string reasonKey = GlobalConstVars.PayCallbackReason.FormatWith(order.OId);
            string solutionKey = GlobalConstVars.PayCallbackSolution.FormatWith(order.OId);

            string reason = _caching.Get<string>(reasonKey);
            int isShowTips = 0;
            if(!reason.IsNullOrEmpty())
            {
                ViewData["PayCallbackReason"] = reason;
                isShowTips=1;
            }
            string solution = _caching.Get<string>(solutionKey);
            if (!solution.IsNullOrEmpty())
            {
                ViewData["PayCallbackSolution"] = solution;
                isShowTips = 1;
            }

            ViewData["IsShowTips"] = isShowTips;

            var responseData = new
            {
                order = order,
                CartData = orderProduct,//购物车产品
                Paymethod = paymethodList,//支付方式
                UserId = CurrentUserId,//用户id
                UserEmail = CurrentUserEmail,//用户币种
                Currency = CurrentCurrency, //返回币种，
                CurrencySymbol = CurrentCurrencySymbol, //返回币种，
                UserSessionId = CurrentUserSessionId,//访客
                OrderCurrency = order.Currency,
                Symbol = currency.Symbol,
                ShippingInfo = shipping,//收货地址
                OId = order.OId,

                PaymentId = order.PaymentId,//支付方式Id

                ordersPrice = ordersPrice,//订单价格明细
                ordersPriceFormat = ordersPriceFormat,//价格格式

            };

            return View(responseData);

        }


    }
}
