<!-- 解析ViewData["VisualPageData"]中的footer配置 -->
{% assign visualPageData = ViewData["VisualPageData"] | jsonparse %}
{% assign footerConfig = null %}
{% assign showFooter = true %}
{% assign showCopyright = true %}
{% assign showPaymentIcons = true %}
{% assign newsletterConfig = null %}
{% assign socialConfig = null %}
{% assign quickMenus = null %}

{% comment %} 从Plugins列表中筛选footer类型的插件 {% endcomment %}
{% assign footerPlugins = visualPageData.Plugins | where: "Type", "footer" %}
{% if footerPlugins and footerPlugins.size > 0 %}
    {% assign footerPlugin = footerPlugins[0] %}
    {% assign footerConfig = footerPlugin %}

    <!-- 解析Settings -->
    {% if footerConfig.Settings %}
        {% assign copyrightValue = footerConfig.Settings.Copyright | append: "" %}
        {% if copyrightValue == "0" %}
            {% assign showCopyright = false %}
        {% endif %}

        {% assign paymentValue = footerConfig.Settings.PaymentMethodIcons | append: "" %}
        {% if paymentValue == "0" %}
            {% assign showPaymentIcons = false %}
        {% endif %}
    {% endif %}

    <!-- 解析Blocks -->
    {% if footerConfig.Blocks %}
        {% assign newsletterConfig = footerConfig.Blocks.Newsletter-1 %}
        {% assign socialConfig = footerConfig.Blocks.Social-1 %}

        <!-- 收集QuickMenu配置 -->
        {% assign quickMenus = "" | split: "," %}
        {% for block in footerConfig.Blocks %}
            {% if block[0] contains "QuickMenu-" %}
                {% assign quickMenus = quickMenus | concat: block[1] %}
            {% endif %}
        {% endfor %}
    {% endif %}
{% endif %}

 {% comment %}Footer调试信息 {% endcomment %}
{% comment %}<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">{% endcomment %}
    {% comment %}<h4>Footer可视化数据调试信息</h4>{% endcomment %}
    {% comment %}{% if footerConfig %}{% endcomment %}
        {% comment %}<p><strong>Footer插件配置:</strong></p>{% endcomment %}
        {% comment %}<ul>{% endcomment %}
            {% comment %}<li>PId: {{ footerConfig.PId }}</li>{% endcomment %}
            {% comment %}<li>Type: {{ footerConfig.Type }}</li>{% endcomment %}
            {% comment %}<li>Mode: {{ footerConfig.Mode }}</li>{% endcomment %}
            {% comment %}<li>Settings.Columns: {{ footerConfig.Settings.Columns }}</li>{% endcomment %}
            {% comment %}<li>Settings.Copyright: {{ footerConfig.Settings.Copyright }} → 转换后: "{{ footerConfig.Settings.Copyright | append: "" }}"</li>{% endcomment %}
            {% comment %}<li>Settings.PaymentMethodIcons: {{ footerConfig.Settings.PaymentMethodIcons }} → 转换后: "{{ footerConfig.Settings.PaymentMethodIcons | append: "" }}"</li>{% endcomment %}
            {% comment %}{% if newsletterConfig %}{% endcomment %}
                {% comment %}<li>Newsletter.Title: {{ newsletterConfig.Title }}</li>{% endcomment %}
                {% comment %}<li>Newsletter.SubTitle: {{ newsletterConfig.SubTitle }}</li>{% endcomment %}
                {% comment %}<li>Newsletter.InputPlaceholder: {{ newsletterConfig.InputPlaceholder }}</li>{% endcomment %}
                {% comment %}<li>Newsletter.ButtonText: {{ newsletterConfig.ButtonText }}</li>{% endcomment %}
                {% comment %}<li>Newsletter.Social: {{ newsletterConfig.Social }}</li>{% endcomment %}
            {% comment %}{% endif %}{% endcomment %}
            {% comment %}{% if socialConfig %}{% endcomment %}
                {% comment %}<li>Social.Title: {{ socialConfig.Title }}</li>{% endcomment %}
                {% comment %}<li>Social.Display: {{ socialConfig.Display }}</li>{% endcomment %}
            {% comment %}{% endif %}{% endcomment %}
            {% comment %}{% if footerConfig.Blocks %}{% endcomment %}
                {% comment %}<li>QuickMenu数量: {% assign quickMenuCount = 0 %}{% for block in footerConfig.Blocks %}{% if block[0] contains "QuickMenu-" %}{% assign quickMenuCount = quickMenuCount | plus: 1 %}{% endif %}{% endfor %}{{ quickMenuCount }}</li>{% endcomment %}
            {% comment %}{% endif %}{% endcomment %}
        {% comment %}</ul>{% endcomment %}
    {% comment %}{% else %}{% endcomment %}
        {% comment %}<p><strong>未找到Footer插件</strong></p>{% endcomment %}
    {% comment %}{% endif %}{% endcomment %}
    {% comment %}<p><strong>当前配置状态:</strong></p>{% endcomment %}
    {% comment %}<ul>{% endcomment %}
        {% comment %}<li>showFooter: {{ showFooter }}</li>{% endcomment %}
        {% comment %}<li>showCopyright: {{ showCopyright }}</li>{% endcomment %}
        {% comment %}<li>showPaymentIcons: {{ showPaymentIcons }}</li>{% endcomment %}
        {% comment %}<li>newsletterConfig: {% if newsletterConfig %}有配置{% else %}无配置{% endif %}</li>{% endcomment %}
        {% comment %}<li>socialConfig: {% if socialConfig %}有配置{% else %}无配置{% endif %}</li>{% endcomment %}
    {% comment %}</ul>{% endcomment %}
{% comment %}</div>{% endcomment %}

<!--Footer-->
{% if showFooter %}
<div class="footer footer-7">
    <div class="footer-top clearfix">
        <div class="container">
            <div class="row">
                <div class="col-12 col-sm-12 col-md-4 col-lg-3 contact-us-col footer-links">
                    <h4 class="h4">Contact Us</h4>
                    <ul>
                        <li style="list-style:none;"><i class="icon an an-phone"></i>&nbsp;+86-156 3907 3051</li>
                        <li style="list-style:none;"><i class="icon an an-envelope-o"></i><a href="mailto:<EMAIL>">&nbsp;<EMAIL></a></li>
                        <li style="list-style:none;"><i class="icon an an-skype"></i>&nbsp;Retekess</li>
                        <li style="list-style:none;"><i class="icon an an-whatsapp"></i>&nbsp;+86-156 3907 3051</li>
                        <li style="list-style:none;"><i class="icon an an-wechat"></i>&nbsp;Retekess</li>
                        <li style="list-style:none;"><i class="icon an an-facebook"></i><a target="_blank" rel="noopener" class="m-1 share-facebook" href="https://www.facebook.com/retekess.es/">&nbsp;Facebook</a></li>
                    </ul>
                </div>

                {% comment %} 动态底部导航菜单 - 通过HTMX加载 {% endcomment %}
                <div hx-get="/home/<USER>" hx-trigger="load" hx-swap="outerHTML">
                    <!-- 底部导航菜单将通过HTMX加载 -->
                </div>

                {% comment %} Newsletter订阅区域 - 根据配置显示 {% endcomment %}
                {% if newsletterConfig %}
                <div class="col-12 col-sm-12 col-md-4 col-lg-3 newsletter-col">
                    <div class="display-table">
                        <div class="display-table-cell footer-newsletter">
                            <form id="form_subscribute" hx-post="/account/OnSubscribute" method="post"
                                  class="form"
                                  hx-target="#subscribe-result"
                                  hx-trigger="submit throttle:2s"
                                  hx-swap="afterbegin"
                                  hx-on::after-request="if(event.detail.successful){ submitCallback(event.detail.xhr.responseText) }" >

                                {% if newsletterConfig.Title %}
                                    <label class="h4">{{ newsletterConfig.Title }}</label>
                                {% else %}
                                    <label class="h4">Suscríbete para Recibir</label>
                                {% endif %}

                                {% if newsletterConfig.SubTitle %}
                                    <p>{{ newsletterConfig.SubTitle }}</p>
                                {% else %}
                                    <p>Últimas noticias, ofertas exclusivas y regalos</p>
                                {% endif %}

                                <div class="input-group">
                                    <input type="email" class="input-group__field newsletter-input"
                                           name="email"
                                           placeholder="{% if newsletterConfig.InputPlaceholder %}{{ newsletterConfig.InputPlaceholder }}{% else %}{{ "web.footer.enter_email"|translate}}{% endif %}"
                                           id="CustomerEmail"
                                           autocapitalize="off"  autocorrect="off"
                                           onkeyup="this.setCustomValidity(''); $(this).removeClass('is-invalid');"
                                           hx-on:htmx:validation:validate="if(this.value == '') {
                                             this.setCustomValidity('{{ "web.footer.enter_email"|translate}}');
                                             $(this).addClass('is-invalid');
                                         }else{$(this).removeClass('is-invalid')}"
                                           />

                                    <span class="input-group__btn">
                                        <button type="submit" class="btn btn-secondary newsletter__submit theme-btn" name="commit" id="Subscribe" hx-on="htmx:configRequest: this.disabled = true; this.innerText = 'Submitting...'" hx-disabled-elt="this" hx-indicator="#spinner">
                                            <span class="newsletter__submit-text--large">
                                                {% if newsletterConfig.ButtonText %}{{ newsletterConfig.ButtonText }}{% else %}Subscribe{% endif %}
                                            </span>
                                        </button>
                                    </span>
                                </div>
                            </form>
                            <div id="subscribe-result" class="mt-2"></div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="footer-bottom clearfix">
        <div class="container">
            {% comment %} 支付方式图标 - 根据配置显示 {% endcomment %}
            {% if showPaymentIcons %}
            <ul class="payment-icons list--inline">
                <li><i class="an an-cc-visa" aria-hidden="true"></i></li>
                <li><i class="an an-cc-mastercard" aria-hidden="true"></i></li>
                <li><i class="an an-cc-amex" aria-hidden="true"></i></li>
                <li><i class="an an-cc-paypal" aria-hidden="true"></i></li>
                <li><i class="an an-cc-discover" aria-hidden="true"></i></li>
                <li><i class="an an-cc-stripe" aria-hidden="true"></i></li>
                <li><i class="an an-cc-jcb" aria-hidden="true"></i></li>
            </ul>
            {% endif %}

            {% comment %} 版权信息 - 根据配置显示 {% endcomment %}
            {% if showCopyright %}
            <div class="copytext">
                Derechos de autor © 2009-2025. Reservados todos los derechos. Desarrollado por Retekess.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}
<!--End Footer-->
<!--Scoll Top-->
<span id="site-scroll"><i class="icon an an-arrow-up"></i></span>
<!--End Scoll Top-->
<style>
    .is-invalid {
        border-color: #dc3545 !important;
        padding-right: calc(1.5em + .75rem) !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e") !important;
        background-repeat: no-repeat !important;
        background-position: right calc(.375em + .1875rem) center !important;
        background-size: calc(.75em + .375rem) calc(.75em + .375rem) !important;
    }
</style>
<script>
    function submitCallback(responseText) {
        const notification = document.getElementById('subscribe-result');
        notification.classList.remove('hidden');
        const response = JSON.parse(responseText);
        notification.innerHTML = `
         <div class="alert alert-${response.status ? 'success' : 'danger'}">
           ${response.msg}
         </div>`;
        if (response.status == true) {       
            document.getElementById('form_subscribute').reset();
        } 
        // 3秒后自动隐藏
        setTimeout(() => {
            notification.classList.add('hidden');
        }, 10000);
    }
</script>