using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.Model.Dto;
using YseStore.Repo;

namespace YseStore.Service
{
    /// <summary>
    /// 
    /// </summary>
    public class OceanpaymentStatesIsoServices : BaseServices<country_states_iso>, IOceanpaymentStatesIsoServices
    {
        private readonly IStringLocalizer<OceanpaymentStatesIsoServices> _localizer;

        private readonly ILogger<OceanpaymentStatesIsoServices> _logger;
        private readonly ICaching _cacheService;

        /// <summary>
        /// 构造函数，通过依赖注入获取数据仓储
        /// </summary>
        /// <param name="baseDal">UrlService</param>
        public OceanpaymentStatesIsoServices(IBaseRepository<country_states_iso> baseDal, ILogger<OceanpaymentStatesIsoServices> logger, ICaching cacheService, IStringLocalizer<OceanpaymentStatesIsoServices> localizer) : base(baseDal)
        {
            _logger = logger;
            this._cacheService = cacheService;
            _localizer = localizer;
        }



    }
}
